# 项目重构总结

## 重构目标

根据合理的分层设计规则，重构项目的DTO结构，解决以下问题：

1. **DTO中有大量的Request和Response内部类**，这些应该拆分出去
2. **CouponDTO和MarketingPriceDTO只是把各种Request和Response组合起来**，没有实际作用
3. **DTO中的类直接传递到Mapper层**，跨层混乱
4. **Request和Response命名不合适**，在gRPC层会混淆
5. **分层设计不合理**：
   - Mapper层应该返回PO，接收PO或Query
   - Service层应该返回BO，接收DTO

## 重构内容

### 1. 新的包结构

```
bean/
├── dto/          # Service层接收参数（原Request改为DTO）
├── bo/           # Service层返回结果（原Response改为BO）
├── query/        # Mapper层查询条件
└── po/           # 数据库实体（保持不变）
```

### 2. 拆分后的类结构

#### DTO类（Service层输入）
- `CreateCouponTemplateDTO` - 创建优惠券模板
- `ClaimCouponDTO` - 领取优惠券
- `QueryCouponTemplatesDTO` - 查询优惠券模板
- `QueryUserCouponsDTO` - 查询用户优惠券
- `QueryUsageRecordsDTO` - 查询使用记录
- `ValidateCouponDTO` - 验证优惠券
- `UseCouponDTO` - 使用优惠券
- `CalculatePriceDTO` - 计算营销价格
- `RecommendCouponsDTO` - 推荐优惠券
- `BatchCalculatePriceDTO` - 批量计算价格

#### BO类（Service层输出）
- `PageBO<T>` - 分页业务对象
- `ValidateCouponBO` - 验证优惠券结果
- `UseCouponBO` - 使用优惠券结果
- `CalculatePriceBO` - 计算价格结果
- `CouponDiscountDetailBO` - 优惠券优惠详情
- `RecommendCouponsBO` - 推荐优惠券结果
- `RecommendedCouponBO` - 推荐优惠券详情
- `BatchCalculatePriceBO` - 批量计算价格结果

#### Query类（Mapper层查询条件）
- `CouponTemplatesQuery` - 优惠券模板查询条件
- `UserCouponsQuery` - 用户优惠券查询条件
- `UsageRecordsQuery` - 使用记录查询条件

### 3. 修改的接口

#### Mapper层接口
- 所有查询方法的参数从Request类型改为Query类型
- 返回值保持PO类型不变

#### Service层接口
- 所有方法的参数从Request类型改为DTO类型
- 所有方法的返回值从Response类型改为BO类型

#### Service实现类
- `MarketingPriceServiceImpl` - 更新所有方法签名和内部实现

### 4. 修改的XML映射文件
- `CouponTemplateMapper.xml` - 更新parameterType为Query类型
- `UserCouponMapper.xml` - 更新parameterType为Query类型
- `CouponUsageRecordMapper.xml` - 更新parameterType为Query类型

### 5. 新增工具类
- `DTOConverter` - 用于在Service层和Mapper层之间转换DTO和Query对象

## 分层设计原则

### Mapper层
- **输入参数**：PO对象或Query对象
- **返回值**：PO对象或PO对象列表
- **职责**：数据访问，不应该接收业务层的DTO

### Service层
- **输入参数**：DTO对象（数据传输对象）
- **返回值**：BO对象（业务对象）
- **职责**：业务逻辑处理，为上层提供业务接口

### gRPC层（未来实现）
- **输入参数**：gRPC Request对象
- **返回值**：gRPC Response对象
- **职责**：协议转换，将gRPC请求转换为Service层的DTO，将Service层的BO转换为gRPC响应

## 重构优势

1. **职责清晰**：每一层都有明确的输入输出类型，避免跨层混乱
2. **命名规范**：DTO用于数据传输，BO用于业务对象，Query用于查询条件，避免与gRPC的Request/Response混淆
3. **易于维护**：类职责单一，修改某一层不会影响其他层
4. **扩展性好**：新增功能时可以按照统一的模式添加对应的DTO、BO、Query类
5. **类型安全**：编译期就能发现类型不匹配的问题

## 后续工作

1. 实现Service层的具体业务逻辑
2. 实现gRPC服务层，使用新的DTO/BO结构
3. 编写单元测试验证重构后的功能正确性
4. 更新相关文档和API说明
