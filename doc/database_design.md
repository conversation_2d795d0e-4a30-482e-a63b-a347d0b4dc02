# 优惠券模块数据库设计

## 概述

优惠券模块采用MySQL作为持久化存储，设计4个核心表来支持完整的优惠券业务流程。所有金额字段统一使用INT类型，单位为分。

## 表结构设计

### 1. 优惠券模板表 (coupon_template)

优惠券模板表用于存储优惠券的基础配置信息，是优惠券系统的核心配置表。

```sql
CREATE TABLE `coupon_template` (
  `id` bigint NOT NULL COMMENT '主键ID',
  `template_name` varchar(100) NOT NULL COMMENT '模板名称',
  `template_type` tinyint NOT NULL COMMENT '优惠券类型：1-满减券，2-折扣券，3-立减券',
  `scope_type` tinyint NOT NULL COMMENT '使用范围类型：1-平台级，2-应用级，3-商品级',
  `scope_value` text COMMENT '范围值：应用级存应用ID，商品级存商品ID列表(JSON)',
  `discount_amount` int DEFAULT NULL COMMENT '优惠金额(分)，满减券和立减券使用',
  `discount_rate` decimal(3,2) DEFAULT NULL COMMENT '折扣率(0.01-0.99)，折扣券使用',
  `min_amount` int DEFAULT NULL COMMENT '最低消费金额(分)，满减券使用',
  `max_discount` int DEFAULT NULL COMMENT '最大优惠金额(分)，折扣券使用',
  `total_count` int NOT NULL COMMENT '发放总数量',
  `per_user_limit` int NOT NULL DEFAULT '1' COMMENT '每用户限领数量',
  `valid_start_time` datetime NOT NULL COMMENT '有效期开始时间',
  `valid_end_time` datetime NOT NULL COMMENT '有效期结束时间',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-启用，0-禁用',
  `description` varchar(500) DEFAULT NULL COMMENT '优惠券描述',
  `create_time` datetime NOT NULL COMMENT '创建时间',
  `update_time` datetime NOT NULL COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  -- 优化后的索引设计
--   KEY `idx_status_valid_time` (`status`, `valid_start_time`, `valid_end_time`),
--   KEY `idx_template_type_scope` (`template_type`, `scope_type`),
--   KEY `idx_scope_type_value` (`scope_type`, `scope_value`(50)),
--   KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠券模板表';
```

### 2. 用户优惠券表 (user_coupon)

用户优惠券表存储用户领取的优惠券实例，每条记录代表一张具体的优惠券。

```sql
CREATE TABLE `user_coupon` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `coupon_code` varchar(32) NOT NULL COMMENT '优惠券码，唯一标识',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `app_id` varchar(50) NOT NULL COMMENT '应用ID',
  `status` tinyint NOT NULL DEFAULT '1' COMMENT '状态：1-未使用，2-已使用，3-已过期',
  `used_time` datetime DEFAULT NULL COMMENT '使用时间',
  `used_order_id` varchar(50) DEFAULT NULL COMMENT '使用的订单ID',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `create_time` datetime NOT NULL  COMMENT '创建时间',
  `update_time` datetime NOT NULL  COMMENT '更新时间',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE KEY `uk_coupon_code` (`coupon_code`),
  -- 优化后的索引设计
  KEY `idx_user_status_expire` (`user_id`, `status`, `expire_time`),
  KEY `idx_user_app_status` (`user_id`, `app_id`, `status`),
  KEY `idx_template_user` (`template_id`, `user_id`),
  KEY `idx_expire_status` (`expire_time`, `status`),
  KEY `idx_used_order` (`used_order_id`),
  KEY `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户优惠券表';
```

### 3. 优惠券发放记录表 (coupon_issue_record)

记录优惠券的发放历史，用于统计分析和问题追踪。

```sql
CREATE TABLE `coupon_issue_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `template_id` bigint NOT NULL COMMENT '模板ID',
  `user_id` varchar(50) NOT NULL COMMENT '用户ID',
  `app_id` varchar(50) NOT NULL COMMENT '应用ID',
  `coupon_code` varchar(32) NOT NULL COMMENT '优惠券码',
  `issue_type` tinyint NOT NULL COMMENT '发放类型：1-用户主动领取，2-系统定时发放，3-活动发放',
  `issue_time` datetime NOT NULL  COMMENT '发放时间',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `create_time` datetime NOT NULL  COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  -- 优化后的索引设计
  KEY `idx_template_issue_time` (`template_id`, `issue_time`),
  KEY `idx_user_issue_time` (`user_id`, `issue_time`),
  KEY `idx_app_issue_time` (`app_id`, `issue_time`),
  KEY `idx_coupon_code` (`coupon_code`),
  KEY `idx_issue_type_time` (`issue_type`, `issue_time`),
  KEY `idx_expire_time` (`expire_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='优惠券发放记录表';
```

## 索引设计说明

### 索引设计原则
1. **高区分度优先**: 优先为高区分度字段建立索引
2. **复合索引优化**: 根据实际查询场景设计复合索引，遵循最左前缀原则
3. **覆盖索引策略**: 对于频繁查询的场景，设计覆盖索引减少回表
4. **避免冗余索引**: 删除低效和冗余的单列索引

### 主要索引策略
1. **主键索引**: 所有表都使用自增ID作为主键
2. **唯一索引**: 优惠券码使用唯一索引保证不重复
3. **业务复合索引**: 根据查询场景设计高效复合索引
4. **时间范围索引**: 支持按时间范围查询的场景

### 查询场景分析与索引优化

#### 1. 用户优惠券表 (user_coupon) 索引优化
- **`idx_user_status_expire(user_id, status, expire_time)`**
  - 场景：查询用户可用优惠券，支持按过期时间排序
  - 覆盖查询：`selectAvailableCoupons`、`selectExpiringSoon`
- **`idx_user_app_status(user_id, app_id, status)`**
  - 场景：按应用查询用户优惠券，支持状态过滤
  - 覆盖查询：`selectByCondition` 中的用户+应用+状态组合查询
- **`idx_template_user(template_id, user_id)`**
  - 场景：统计用户已领取的优惠券数量
  - 覆盖查询：`countUserClaimedCoupons`
- **`idx_expire_status(expire_time, status)`**
  - 场景：批量过期处理，查询即将过期的优惠券
  - 覆盖查询：`selectExpiringSoon`、批量过期任务

#### 2. 优惠券模板表 (coupon_template) 索引优化
- **`idx_status_valid_time(status, valid_start_time, valid_end_time)`**
  - 场景：查询有效的模板，支持时间范围过滤
  - 覆盖查询：`selectValidTemplates`、`selectExpiringSoon`
- **`idx_template_type_scope(template_type, scope_type)`**
  - 场景：按类型和范围查询模板
  - 覆盖查询：`selectByCondition` 中的类型+范围组合查询
- **`idx_scope_type_value(scope_type, scope_value)`**
  - 场景：按范围类型和值查询模板
  - 覆盖查询：`selectByScopeTypeAndValue`

#### 3. 发放记录表 (coupon_issue_record) 索引优化
- **`idx_template_issue_time(template_id, issue_time)`**
  - 场景：按模板统计发放记录，支持时间范围查询
- **`idx_user_issue_time(user_id, issue_time)`**
  - 场景：查询用户发放历史
- **`idx_app_issue_time(app_id, issue_time)`**
  - 场景：按应用统计发放情况

#### 4. 使用记录表 (coupon_usage_record) 索引优化
- **`uk_order_coupon(order_id, coupon_code)`**
  - 场景：防止同一订单重复使用同一优惠券
- **`idx_template_used_time(template_id, used_time)`**
  - 场景：按模板统计使用情况
- **`idx_user_used_time(user_id, used_time)`**
  - 场景：查询用户使用历史

## 分表策略

### 当前设计
- 单表设计，适合中小规模业务
- 通过索引优化查询性能
- 支持后续分表扩展

### 扩展方案
1. **用户维度分表**: 按用户ID哈希分表
2. **时间维度分表**: 按月份分表存储历史数据
3. **应用维度分表**: 按应用ID分表隔离数据

## 数据一致性保证

### 应用层事务
- 优惠券发放使用数据库事务保证一致性
- 缓存更新采用最终一致性策略
- 关键操作增加分布式锁控制并发

### 数据校验
- 发放数量通过Redis计数器控制
- 用户领取限制通过缓存+数据库双重校验
- 优惠券使用状态通过乐观锁更新

## 索引优化迁移方案

### 优化前后对比
| 表名 | 优化前索引数量 | 优化后索引数量 | 主要改进 |
|------|---------------|---------------|----------|
| coupon_template | 4个单列索引 | 4个复合索引 | 删除低区分度单列索引，增加复合索引 |
| user_coupon | 7个索引(5单列+2复合) | 6个复合索引 | 删除低效单列索引，优化复合索引 |
| coupon_issue_record | 6个单列索引 | 6个复合索引 | 全部改为复合索引，提升查询效率 |
| coupon_usage_record | 6个单列索引 | 6个索引(1唯一+5复合) | 增加唯一约束，优化复合索引 |

### 迁移脚本

```sql
-- ===== 优惠券模板表索引优化 =====
-- 删除原有低效索引
ALTER TABLE coupon_template DROP INDEX idx_template_type;
ALTER TABLE coupon_template DROP INDEX idx_scope_type;
ALTER TABLE coupon_template DROP INDEX idx_status;
ALTER TABLE coupon_template DROP INDEX idx_valid_time;

-- 创建优化后的复合索引
ALTER TABLE coupon_template ADD INDEX idx_status_valid_time (status, valid_start_time, valid_end_time);
ALTER TABLE coupon_template ADD INDEX idx_template_type_scope (template_type, scope_type);
ALTER TABLE coupon_template ADD INDEX idx_scope_type_value (scope_type, scope_value(50));
ALTER TABLE coupon_template ADD INDEX idx_create_time (create_time);

-- ===== 用户优惠券表索引优化 =====
-- 删除原有低效索引
ALTER TABLE user_coupon DROP INDEX idx_template_id;
ALTER TABLE user_coupon DROP INDEX idx_user_id;
ALTER TABLE user_coupon DROP INDEX idx_app_id;
ALTER TABLE user_coupon DROP INDEX idx_status;
ALTER TABLE user_coupon DROP INDEX idx_expire_time;
ALTER TABLE user_coupon DROP INDEX idx_user_status;
ALTER TABLE user_coupon DROP INDEX idx_user_app;

-- 创建优化后的复合索引
ALTER TABLE user_coupon ADD INDEX idx_user_status_expire (user_id, status, expire_time);
ALTER TABLE user_coupon ADD INDEX idx_user_app_status (user_id, app_id, status);
ALTER TABLE user_coupon ADD INDEX idx_template_user (template_id, user_id);
ALTER TABLE user_coupon ADD INDEX idx_expire_status (expire_time, status);
ALTER TABLE user_coupon ADD INDEX idx_used_order (used_order_id);
ALTER TABLE user_coupon ADD INDEX idx_create_time (create_time);

-- ===== 发放记录表索引优化 =====
-- 删除原有单列索引
ALTER TABLE coupon_issue_record DROP INDEX idx_template_id;
ALTER TABLE coupon_issue_record DROP INDEX idx_user_id;
ALTER TABLE coupon_issue_record DROP INDEX idx_app_id;
ALTER TABLE coupon_issue_record DROP INDEX idx_issue_type;
ALTER TABLE coupon_issue_record DROP INDEX idx_issue_time;

-- 创建优化后的复合索引
ALTER TABLE coupon_issue_record ADD INDEX idx_template_issue_time (template_id, issue_time);
ALTER TABLE coupon_issue_record ADD INDEX idx_user_issue_time (user_id, issue_time);
ALTER TABLE coupon_issue_record ADD INDEX idx_app_issue_time (app_id, issue_time);
ALTER TABLE coupon_issue_record ADD INDEX idx_issue_type_time (issue_type, issue_time);
ALTER TABLE coupon_issue_record ADD INDEX idx_expire_time (expire_time);

-- ===== 使用记录表索引优化 =====
-- 删除原有单列索引
ALTER TABLE coupon_usage_record DROP INDEX idx_template_id;
ALTER TABLE coupon_usage_record DROP INDEX idx_user_id;
ALTER TABLE coupon_usage_record DROP INDEX idx_app_id;
ALTER TABLE coupon_usage_record DROP INDEX idx_order_id;

-- 创建优化后的索引
ALTER TABLE coupon_usage_record ADD UNIQUE INDEX uk_order_coupon (order_id, coupon_code);
ALTER TABLE coupon_usage_record ADD INDEX idx_template_used_time (template_id, used_time);
ALTER TABLE coupon_usage_record ADD INDEX idx_user_used_time (user_id, used_time);
ALTER TABLE coupon_usage_record ADD INDEX idx_app_used_time (app_id, used_time);
```

## 性能优化建议

### 索引使用最佳实践
1. **查询优化**：确保WHERE条件遵循最左前缀原则
2. **排序优化**：ORDER BY字段应包含在复合索引中
3. **覆盖索引**：对于频繁查询的字段，考虑包含在索引中避免回表
4. **索引监控**：定期分析慢查询日志，优化索引使用

### 读写分离
- 查询操作使用从库
- 写操作使用主库
- 缓存承担大部分读压力

### 缓存策略
- 热点模板数据缓存
- 用户优惠券列表缓存
- 发放计数器使用Redis原子操作

### 批量操作
- 支持批量发放优惠券
- 批量过期处理
- 批量状态更新
