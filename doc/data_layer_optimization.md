# 数据层实现优化方案

## 概述

基于索引优化后的数据库设计，对数据层实现进行相应优化，包括SQL查询优化、缓存策略优化和查询方法改进。

## 1. SQL查询优化

### 1.1 用户优惠券查询优化

#### 原有查询问题
- 使用了低区分度的单列索引
- 复合查询条件没有充分利用索引
- 排序字段不在索引中

#### 优化后的查询策略
```sql
-- 查询用户可用优惠券（利用 idx_user_status_expire 索引）
SELECT uc.*, ct.* 
FROM user_coupon uc 
LEFT JOIN coupon_template ct ON uc.template_id = ct.id
WHERE uc.user_id = ? 
  AND uc.status = 1 
  AND uc.expire_time > NOW()
ORDER BY uc.expire_time ASC;

-- 按用户和应用查询（利用 idx_user_app_status 索引）
SELECT uc.*, ct.* 
FROM user_coupon uc 
LEFT JOIN coupon_template ct ON uc.template_id = ct.id
WHERE uc.user_id = ? 
  AND uc.app_id = ? 
  AND uc.status = ?
ORDER BY uc.create_time DESC;
```

### 1.2 模板查询优化

#### 优化后的查询策略
```sql
-- 查询有效模板（利用 idx_status_valid_time 索引）
SELECT * FROM coupon_template 
WHERE status = 1 
  AND valid_start_time <= NOW() 
  AND valid_end_time > NOW()
ORDER BY create_time DESC;

-- 按类型和范围查询（利用 idx_template_type_scope 索引）
SELECT * FROM coupon_template 
WHERE template_type = ? 
  AND scope_type = ?
  AND status = 1
ORDER BY create_time DESC;
```

### 1.3 统计查询优化

#### 优化后的统计查询
```sql
-- 统计用户已领取数量（利用 idx_template_user 索引）
SELECT COUNT(1) FROM user_coupon 
WHERE template_id = ? AND user_id = ?;

-- 批量过期处理（利用 idx_expire_status 索引）
SELECT coupon_code FROM user_coupon 
WHERE expire_time <= ? AND status = 1;
```

## 2. Mapper层优化

### 2.1 新增高效查询方法

#### UserCouponMapper 优化
```java
/**
 * 查询用户在指定应用下的可用优惠券（优化版）
 * 利用 idx_user_status_expire 索引
 */
List<UserCouponBO> selectAvailableCouponsOptimized(
    @Param("userId") String userId, 
    @Param("appId") String appId,
    @Param("currentTime") LocalDateTime currentTime);

/**
 * 批量查询即将过期的优惠券（优化版）
 * 利用 idx_expire_status 索引
 */
List<UserCouponPO> selectExpiringSoonOptimized(
    @Param("expireTime") LocalDateTime expireTime,
    @Param("batchSize") Integer batchSize);

/**
 * 统计用户在指定模板下的领取数量（优化版）
 * 利用 idx_template_user 索引
 */
Integer countUserClaimedCouponsOptimized(
    @Param("templateId") Long templateId,
    @Param("userId") String userId);
```

#### CouponTemplateMapper 优化
```java
/**
 * 查询指定范围内的有效模板（优化版）
 * 利用 idx_status_valid_time 索引
 */
List<CouponTemplatePO> selectValidTemplatesOptimized(
    @Param("currentTime") LocalDateTime currentTime);

/**
 * 按类型和范围查询模板（优化版）
 * 利用 idx_template_type_scope 索引
 */
List<CouponTemplatePO> selectByTypeAndScopeOptimized(
    @Param("templateType") Integer templateType,
    @Param("scopeType") Integer scopeType);
```

### 2.2 分页查询优化

#### 优化分页查询性能
```java
/**
 * 优化后的分页查询，使用覆盖索引
 */
@Select("SELECT id FROM user_coupon " +
        "WHERE user_id = #{userId} AND status = #{status} " +
        "ORDER BY expire_time ASC " +
        "LIMIT #{offset}, #{pageSize}")
List<Long> selectUserCouponIds(UserCouponsQuery query);

/**
 * 根据ID列表批量查询详情
 */
List<UserCouponBO> selectDetailsByIds(@Param("ids") List<Long> ids);
```

## 3. 缓存策略优化

### 3.1 多级缓存设计

#### L1缓存：本地缓存
```java
@Component
public class CouponLocalCache {
    
    private final Cache<Long, CouponTemplateBO> templateCache = 
        Caffeine.newBuilder()
            .maximumSize(1000)
            .expireAfterWrite(5, TimeUnit.MINUTES)
            .build();
    
    private final Cache<String, List<UserCouponBO>> userCouponCache = 
        Caffeine.newBuilder()
            .maximumSize(10000)
            .expireAfterWrite(2, TimeUnit.MINUTES)
            .build();
}
```

#### L2缓存：Redis缓存
```java
@Component
public class CouponRedisCache {
    
    /**
     * 缓存用户可用优惠券列表
     * Key: user_coupons:{userId}:{appId}
     * TTL: 10分钟
     */
    public void cacheUserAvailableCoupons(String userId, String appId, 
                                         List<UserCouponBO> coupons) {
        String key = String.format("user_coupons:%s:%s", userId, appId);
        redisTemplate.opsForValue().set(key, coupons, Duration.ofMinutes(10));
    }
    
    /**
     * 缓存模板信息
     * Key: template:{templateId}
     * TTL: 30分钟
     */
    public void cacheTemplate(CouponTemplateBO template) {
        String key = "template:" + template.getId();
        redisTemplate.opsForValue().set(key, template, Duration.ofMinutes(30));
    }
}
```

### 3.2 缓存更新策略

#### 写入时更新缓存
```java
@Service
public class CouponCacheService {
    
    /**
     * 优惠券使用后更新缓存
     */
    @CacheEvict(value = "userCoupons", key = "#userId + ':' + #appId")
    public void evictUserCouponsCache(String userId, String appId) {
        // 清除用户优惠券缓存
    }
    
    /**
     * 模板更新后清除相关缓存
     */
    @CacheEvict(value = "templates", key = "#templateId")
    public void evictTemplateCache(Long templateId) {
        // 清除模板缓存
    }
}
```

## 4. 查询性能监控

### 4.1 慢查询监控
```java
@Aspect
@Component
public class QueryPerformanceAspect {
    
    @Around("execution(* com.blsc.marketing.*.mapper.*.*(..))")
    public Object monitorQueryPerformance(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        try {
            Object result = joinPoint.proceed();
            long duration = System.currentTimeMillis() - startTime;
            
            if (duration > 100) { // 超过100ms记录慢查询
                log.warn("Slow query detected: {} took {}ms", 
                        joinPoint.getSignature(), duration);
            }
            
            return result;
        } catch (Exception e) {
            log.error("Query failed: {}", joinPoint.getSignature(), e);
            throw e;
        }
    }
}
```

### 4.2 索引使用情况监控
```java
@Component
public class IndexUsageMonitor {
    
    /**
     * 定期检查索引使用情况
     */
    @Scheduled(fixedRate = 3600000) // 每小时执行一次
    public void checkIndexUsage() {
        // 查询索引使用统计
        List<Map<String, Object>> indexStats = jdbcTemplate.queryForList(
            "SELECT table_name, index_name, cardinality " +
            "FROM information_schema.statistics " +
            "WHERE table_schema = DATABASE() " +
            "AND table_name IN ('coupon_template', 'user_coupon', 'coupon_issue_record', 'coupon_usage_record')"
        );
        
        // 分析并记录索引使用情况
        analyzeIndexUsage(indexStats);
    }
}
```

## 5. 实施建议

### 5.1 分阶段实施
1. **第一阶段**：执行索引优化脚本
2. **第二阶段**：更新Mapper查询方法
3. **第三阶段**：实施缓存优化
4. **第四阶段**：部署性能监控

### 5.2 性能测试
- 在测试环境验证索引优化效果
- 对比优化前后的查询性能
- 压力测试验证系统稳定性

### 5.3 回滚方案
- 保留原有索引的创建脚本
- 准备快速回滚的SQL脚本
- 监控优化后的系统表现

## 6. 预期效果

### 6.1 性能提升
- 用户优惠券查询性能提升60%以上
- 模板查询性能提升80%以上
- 统计查询性能提升70%以上

### 6.2 资源优化
- 索引存储空间减少30%
- 数据库CPU使用率降低20%
- 缓存命中率提升到90%以上

### 6.3 用户体验
- 优惠券列表加载时间从500ms降低到200ms以内
- 优惠券推荐响应时间从300ms降低到100ms以内
- 系统整体响应速度提升40%
