spring:
  application:
    name: marketing-provider
  profiles:
    active: dev

---
spring:
  config:
    activate:
      on-profile: dev
  cloud:
    nacos:
      config:
        server-addr: 192.168.51.50:8848
        file-extension: yaml
        namespace: 03292946-0434-4fde-ab29-b2a2285780aa
        shared-configs[0]:
          data-id: rules-dev.yaml
          refresh: true
logging:
  config: classpath:logback-dev.xml

---
spring:
  config:
    activate:
      on-profile: test
  cloud:
    nacos:
      config:
        server-addr: 192.168.51.50:8848
        file-extension: yaml
        namespace: 71a2b918-91b7-488d-b028-0339838a33a4
        shared-configs[0]:
          data-id: rules-test.yaml
          refresh: true
logging:
  config: classpath:logback-dev.xml

---
spring:
  config:
    activate:
      on-profile: prod
  cloud:
    nacos:
      config:
        server-addr: 192.168.0.235:9001
        file-extension: yaml
        namespace: 39fd3096-ee09-4f9b-9fe9-5a7139907ccb
        shared-configs[0]:
          data-id: rules-prod.yaml
          refresh: true
      username: nacos
      password: LyNF2Og5ku
logging:
  config: classpath:logback-prod.xml