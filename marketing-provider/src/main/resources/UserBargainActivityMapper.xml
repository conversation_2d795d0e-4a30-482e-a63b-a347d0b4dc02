<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blsc.marketing.bargain.mapper.UserBargainActivityMapper">

    <resultMap id="BaseResultMap" type="com.blsc.marketing.bargain.bean.po.UserBargainActivityPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="activity_id" property="activityId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="product_id" property="productId" jdbcType="VARCHAR"/>
        <result column="original_price" property="originalPrice" jdbcType="INTEGER"/>
        <result column="floor_price" property="floorPrice" jdbcType="INTEGER"/>
        <result column="current_price" property="currentPrice" jdbcType="INTEGER"/>
        <result column="total_bargain_amount" property="totalBargainAmount" jdbcType="INTEGER"/>
        <result column="assist_count" property="assistCount" jdbcType="INTEGER"/>
        <result column="min_assist_count" property="minAssistCount" jdbcType="INTEGER"/>
        <result column="status" property="status" jdbcType="INTEGER"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="success_time" property="successTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, activity_id, user_id, app_id, product_id, original_price, floor_price,
        current_price, total_bargain_amount, assist_count, min_assist_count, status,
        expire_time, success_time, create_time, update_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_bargain_activity
        WHERE id = #{id}
    </select>

    <select id="selectByUserAndActivity" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_bargain_activity
        WHERE user_id = #{userId} AND activity_id = #{activityId}
        LIMIT 1
    </select>

    <select id="selectByUserWithPage" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_bargain_activity
        WHERE user_id = #{userId} AND app_id = #{appId}
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <select id="countByUser" resultType="int">
        SELECT COUNT(1)
        FROM user_bargain_activity
        WHERE user_id = #{userId} AND app_id = #{appId}
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <insert id="insertOne" parameterType="com.blsc.marketing.bargain.bean.po.UserBargainActivityPO">
        INSERT INTO user_bargain_activity (
            id, activity_id, user_id, app_id, product_id, original_price, floor_price,
            current_price, total_bargain_amount, assist_count, min_assist_count, status,
            expire_time, success_time, create_time, update_time
        ) VALUES (
            #{id}, #{activityId}, #{userId}, #{appId}, #{productId}, #{originalPrice}, #{floorPrice},
            #{currentPrice}, #{totalBargainAmount}, #{assistCount}, #{minAssistCount}, #{status},
            #{expireTime}, #{successTime}, #{createTime}, #{updateTime}
        )
    </insert>

    <update id="updateById" parameterType="com.blsc.marketing.bargain.bean.po.UserBargainActivityPO">
        UPDATE user_bargain_activity
        SET activity_id = #{activityId},
            user_id = #{userId},
            app_id = #{appId},
            product_id = #{productId},
            original_price = #{originalPrice},
            floor_price = #{floorPrice},
            current_price = #{currentPrice},
            total_bargain_amount = #{totalBargainAmount},
            assist_count = #{assistCount},
            min_assist_count = #{minAssistCount},
            status = #{status},
            expire_time = #{expireTime},
            success_time = #{successTime},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <update id="expireActivities">
        UPDATE user_bargain_activity
        SET status = 3, update_time = #{currentTime}
        WHERE status = 1 AND expire_time &lt; #{currentTime}
    </update>

    <delete id="deleteById">
        DELETE FROM user_bargain_activity WHERE id = #{id}
    </delete>

</mapper>
