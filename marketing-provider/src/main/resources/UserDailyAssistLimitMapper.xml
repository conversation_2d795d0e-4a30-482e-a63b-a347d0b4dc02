<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blsc.marketing.bargain.mapper.UserDailyAssistLimitMapper">

    <resultMap id="BaseResultMap" type="com.blsc.marketing.bargain.bean.po.UserDailyAssistLimitPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="assist_date" property="assistDate" jdbcType="DATE"/>
        <result column="assist_count" property="assistCount" jdbcType="INTEGER"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id, user_id, assist_date, assist_count, app_id, create_time, update_time
    </sql>

    <select id="selectById" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_daily_assist_limit
        WHERE id = #{id}
    </select>

    <select id="selectByUserAndDate" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_daily_assist_limit
        WHERE user_id = #{userId} AND assist_date = #{assistDate} AND app_id = #{appId}
        LIMIT 1
    </select>

    <select id="selectByUser" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_daily_assist_limit
        WHERE user_id = #{userId} AND app_id = #{appId}
        ORDER BY assist_date DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <insert id="insertOne" parameterType="com.blsc.marketing.bargain.bean.po.UserDailyAssistLimitPO">
        INSERT INTO user_daily_assist_limit (
            id, user_id, assist_date, assist_count, app_id, create_time, update_time
        ) VALUES (
            #{id}, #{userId}, #{assistDate}, #{assistCount}, #{appId}, #{createTime}, #{updateTime}
        )
    </insert>

    <update id="updateById" parameterType="com.blsc.marketing.bargain.bean.po.UserDailyAssistLimitPO">
        UPDATE user_daily_assist_limit
        SET user_id = #{userId},
            assist_date = #{assistDate},
            assist_count = #{assistCount},
            app_id = #{appId},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <delete id="cleanExpiredRecords">
        DELETE FROM user_daily_assist_limit WHERE assist_date &lt; #{beforeDate}
    </delete>

    <delete id="deleteById">
        DELETE FROM user_daily_assist_limit WHERE id = #{id}
    </delete>

</mapper>
