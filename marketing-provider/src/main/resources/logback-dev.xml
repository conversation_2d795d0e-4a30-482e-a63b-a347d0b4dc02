<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="1 seconds">
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    <springProperty scope="context" name="appName" source="spring.application.name"/>
    <springProperty scope="context" name="environment" source="spring.profiles.active"/>

    <contextName>logback</contextName>
    <appender name="Logstash" class="net.logstash.logback.appender.LogstashTcpSocketAppender">
        <destination>192.168.51.20:4560</destination>
        <encoder charset="UTF-8" class="net.logstash.logback.encoder.LogstashEncoder">
            <!--添加applicationName字段 -->
            <customFields>{"applicationName":"ticket-provider"}</customFields>
        </encoder>
    </appender>

    <logger name="com.blsc.ticket" level="INFO">
        <appender-ref ref="Logstash"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>