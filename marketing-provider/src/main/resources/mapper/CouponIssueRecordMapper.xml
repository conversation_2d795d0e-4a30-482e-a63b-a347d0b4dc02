<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blsc.marketing.coupon.mapper.CouponIssueRecordMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.blsc.marketing.coupon.bean.po.CouponIssueRecordPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="template_id" property="templateId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="coupon_code" property="couponCode" jdbcType="VARCHAR"/>
        <result column="issue_type" property="issueType" jdbcType="TINYINT"/>
        <result column="issue_time" property="issueTime" jdbcType="TIMESTAMP"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, template_id, user_id, app_id, coupon_code, issue_type, issue_time, expire_time, create_time
    </sql>

    <!-- 插入发放记录 -->
    <insert id="insert" parameterType="com.blsc.marketing.coupon.bean.po.CouponIssueRecordPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO coupon_issue_record (
            template_id, user_id, app_id, coupon_code, issue_type, issue_time, expire_time, create_time
        ) VALUES (
            #{templateId}, #{userId}, #{appId}, #{couponCode}, #{issueType}, #{issueTime}, #{expireTime}, #{createTime}
        )
    </insert>

    <!-- 批量插入发放记录 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO coupon_issue_record (
            template_id, user_id, app_id, coupon_code, issue_type, issue_time, expire_time, create_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.templateId}, #{item.userId}, #{item.appId}, #{item.couponCode}, 
             #{item.issueType}, #{item.issueTime}, #{item.expireTime}, #{item.createTime})
        </foreach>
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_issue_record
        WHERE id = #{id}
    </select>

    <!-- 根据优惠券码查询 -->
    <select id="selectByCouponCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_issue_record
        WHERE coupon_code = #{couponCode}
    </select>

    <!-- 根据用户ID查询 -->
    <select id="selectByUserId" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_issue_record
        WHERE user_id = #{userId}
        ORDER BY issue_time DESC
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <!-- 根据模板ID查询 -->
    <select id="selectByTemplateId" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_issue_record
        WHERE template_id = #{templateId}
        ORDER BY issue_time DESC
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <!-- 根据应用ID查询 -->
    <select id="selectByAppId" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_issue_record
        WHERE app_id = #{appId}
        ORDER BY issue_time DESC
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <!-- 根据发放类型查询 -->
    <select id="selectByIssueType" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_issue_record
        WHERE issue_type = #{issueType}
        <if test="startTime != null">
            AND issue_time >= #{startTime}
        </if>
        <if test="endTime != null">
            AND issue_time &lt;= #{endTime}
        </if>
        ORDER BY issue_time DESC
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

    <!-- 统计用户在指定模板下的发放次数 -->
    <select id="countUserIssueRecords" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM coupon_issue_record
        WHERE user_id = #{userId} AND template_id = #{templateId}
    </select>

    <!-- 统计模板的总发放次数 -->
    <select id="countTemplateIssueRecords" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM coupon_issue_record
        WHERE template_id = #{templateId}
    </select>

    <!-- 根据时间范围统计发放记录 -->
    <select id="countIssueRecordsByTimeRange" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM coupon_issue_record
        WHERE issue_time >= #{startTime} AND issue_time &lt;= #{endTime}
        <if test="templateId != null">
            AND template_id = #{templateId}
        </if>
        <if test="appId != null and appId != ''">
            AND app_id = #{appId}
        </if>
    </select>

    <!-- 查询指定时间范围内的发放记录 -->
    <select id="selectByTimeRange" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_issue_record
        WHERE issue_time >= #{startTime} AND issue_time &lt;= #{endTime}
        ORDER BY issue_time DESC
        LIMIT #{pageSize} OFFSET #{offset}
    </select>

</mapper>
