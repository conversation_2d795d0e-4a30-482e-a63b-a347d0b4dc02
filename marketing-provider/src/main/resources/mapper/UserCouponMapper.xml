<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blsc.marketing.coupon.mapper.UserCouponMapper">

    <!-- 用户优惠券结果映射 -->
    <resultMap id="BaseResultMap" type="com.blsc.marketing.coupon.bean.po.UserCouponPO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="coupon_code" property="couponCode" jdbcType="VARCHAR"/>
        <result column="template_id" property="templateId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="used_time" property="usedTime" jdbcType="TIMESTAMP"/>
        <result column="used_order_id" property="usedOrderId" jdbcType="VARCHAR"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
    </resultMap>

    <!-- 用户优惠券详情结果映射（包含模板信息） -->
    <resultMap id="UserCouponDetailResultMap" type="com.blsc.marketing.coupon.bean.bo.UserCouponBO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="coupon_code" property="couponCode" jdbcType="VARCHAR"/>
        <result column="template_id" property="templateId" jdbcType="BIGINT"/>
        <result column="user_id" property="userId" jdbcType="VARCHAR"/>
        <result column="app_id" property="appId" jdbcType="VARCHAR"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="used_time" property="usedTime" jdbcType="TIMESTAMP"/>
        <result column="used_order_id" property="usedOrderId" jdbcType="VARCHAR"/>
        <result column="expire_time" property="expireTime" jdbcType="TIMESTAMP"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
        <!-- 模板信息 -->
        <result column="template_name" property="templateName" jdbcType="VARCHAR"/>
        <result column="template_type" property="templateType" jdbcType="TINYINT"/>
        <result column="scope_type" property="scopeType" jdbcType="TINYINT"/>
        <result column="scope_value" property="scopeValue" jdbcType="VARCHAR"/>
        <result column="discount_amount" property="discountAmount" jdbcType="INTEGER"/>
        <result column="discount_rate" property="discountRate" jdbcType="DECIMAL"/>
        <result column="min_amount" property="minAmount" jdbcType="INTEGER"/>
        <result column="max_discount" property="maxDiscount" jdbcType="INTEGER"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        uc.id, uc.coupon_code, uc.template_id, uc.user_id, uc.app_id, uc.status,
        uc.used_time, uc.used_order_id, uc.expire_time, uc.create_time, uc.update_time
    </sql>

    <!-- 详情字段（包含模板信息） -->
    <sql id="Detail_Column_List">
        uc.id, uc.coupon_code, uc.template_id, uc.user_id, uc.app_id, uc.status,
        uc.used_time, uc.used_order_id, uc.expire_time, uc.create_time, uc.update_time,
        ct.template_name, ct.template_type, ct.scope_type, ct.scope_value,
        ct.discount_amount, ct.discount_rate, ct.min_amount, ct.max_discount, ct.description
    </sql>

    <!-- 插入用户优惠券 -->
    <insert id="insert" parameterType="com.blsc.marketing.coupon.bean.po.UserCouponPO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO user_coupon (
            coupon_code, template_id, user_id, app_id, status,
            used_time, used_order_id, expire_time, create_time, update_time
        ) VALUES (
            #{couponCode}, #{templateId}, #{userId}, #{appId}, #{status},
            #{usedTime}, #{usedOrderId}, #{expireTime}, #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 批量插入用户优惠券 -->
    <insert id="batchInsert" parameterType="java.util.List">
        INSERT INTO user_coupon (
            coupon_code, template_id, user_id, app_id, status,
            expire_time, create_time, update_time
        ) VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.couponCode}, #{item.templateId}, #{item.userId}, #{item.appId}, #{item.status},
             #{item.expireTime}, #{item.createTime}, #{item.updateTime})
        </foreach>
    </insert>

    <!-- 根据优惠券码查询 -->
    <select id="selectByCouponCode" parameterType="java.lang.String" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_coupon uc
        WHERE uc.coupon_code = #{couponCode}
    </select>

    <!-- 根据优惠券码列表查询 -->
    <select id="selectByCouponCodes" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_coupon uc
        WHERE uc.coupon_code IN
        <foreach collection="list" item="couponCode" open="(" separator="," close=")">
            #{couponCode}
        </foreach>
    </select>

    <!-- 根据优惠券码查询详情（包含模板信息） -->
    <select id="selectDetailByCouponCode" parameterType="java.lang.String" resultMap="UserCouponDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM user_coupon uc
        LEFT JOIN coupon_template ct ON uc.template_id = ct.id
        WHERE uc.coupon_code = #{couponCode}
    </select>

    <!-- 根据优惠券码列表查询详情（包含模板信息） -->
    <select id="selectDetailByCouponCodes" resultMap="UserCouponDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM user_coupon uc
        LEFT JOIN coupon_template ct ON uc.template_id = ct.id
        WHERE uc.coupon_code IN
        <foreach collection="list" item="couponCode" open="(" separator="," close=")">
            #{couponCode}
        </foreach>
    </select>

    <!-- 根据条件分页查询用户优惠券 -->
    <select id="selectByCondition" parameterType="com.blsc.marketing.coupon.bean.query.UserCouponsQuery" resultMap="UserCouponDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM user_coupon uc
        LEFT JOIN coupon_template ct ON uc.template_id = ct.id
        <where>
            <if test="userId != null and userId != ''">
                AND uc.user_id = #{userId}
            </if>
            <if test="appId != null and appId != ''">
                AND uc.app_id = #{appId}
            </if>
            <if test="status != null">
                AND uc.status = #{status}
            </if>
            <if test="templateType != null">
                AND ct.template_type = #{templateType}
            </if>
        </where>
        ORDER BY uc.create_time DESC
    </select>

    <!-- 统计查询条件下的总数 -->
    <select id="countByCondition" parameterType="com.blsc.marketing.coupon.bean.query.UserCouponsQuery" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM user_coupon uc
        LEFT JOIN coupon_template ct ON uc.template_id = ct.id
        <where>
            <if test="userId != null and userId != ''">
                AND uc.user_id = #{userId}
            </if>
            <if test="appId != null and appId != ''">
                AND uc.app_id = #{appId}
            </if>
            <if test="status != null">
                AND uc.status = #{status}
            </if>
            <if test="templateType != null">
                AND ct.template_type = #{templateType}
            </if>
        </where>
    </select>

    <!-- 更新用户优惠券状态 -->
    <update id="updateStatus" parameterType="map">
        UPDATE user_coupon
        SET status = #{status},
            used_time = #{usedTime},
            used_order_id = #{usedOrderId},
            update_time = #{updateTime}
        WHERE coupon_code = #{couponCode}
    </update>

    <!-- 统计用户已领取的优惠券数量 -->
    <select id="countUserClaimedCoupons" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM user_coupon
        WHERE user_id = #{userId} AND template_id = #{templateId}
    </select>

    <!-- 统计模板已发放的优惠券数量 -->
    <select id="countIssuedCoupons" parameterType="java.lang.Long" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM user_coupon
        WHERE template_id = #{templateId}
    </select>

    <!-- 查询用户可用的优惠券 -->
    <select id="selectAvailableCoupons" parameterType="map" resultMap="UserCouponDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM user_coupon uc
        LEFT JOIN coupon_template ct ON uc.template_id = ct.id
        WHERE uc.user_id = #{userId}
          AND uc.status = 1
          AND uc.expire_time > NOW()
        <if test="appId != null and appId != ''">
            AND (ct.scope_type = 1 OR (ct.scope_type = 2 AND ct.scope_value = #{appId}))
        </if>
        ORDER BY uc.expire_time ASC
    </select>

    <!-- 查询即将过期的优惠券 -->
    <select id="selectExpiringSoon" parameterType="java.time.LocalDateTime" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_coupon uc
        WHERE uc.status = 1
          AND uc.expire_time > NOW()
          AND uc.expire_time &lt;= #{expireTime}
        ORDER BY uc.expire_time ASC
    </select>

    <!-- 优化版：批量查询即将过期的优惠券（利用 idx_expire_status 索引） -->
    <select id="selectExpiringSoonOptimized" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM user_coupon uc
        WHERE uc.expire_time &lt;= #{expireTime}
          AND uc.status = 1
        ORDER BY uc.expire_time ASC
        <if test="batchSize != null">
            LIMIT #{batchSize}
        </if>
    </select>

    <!-- 优化版：查询用户在指定应用下的可用优惠券（利用 idx_user_status_expire 索引） -->
    <select id="selectAvailableCouponsOptimized" parameterType="map" resultMap="UserCouponDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM user_coupon uc
        LEFT JOIN coupon_template ct ON uc.template_id = ct.id
        WHERE uc.user_id = #{userId}
          AND uc.status = 1
          AND uc.expire_time > #{currentTime}
        <if test="appId != null and appId != ''">
            AND (ct.scope_type = 1 OR (ct.scope_type = 2 AND ct.scope_value = #{appId}))
        </if>
        ORDER BY uc.expire_time ASC
    </select>

    <!-- 优化版：统计用户在指定模板下的领取数量（利用 idx_template_user 索引） -->
    <select id="countUserClaimedCouponsOptimized" parameterType="map" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM user_coupon
        WHERE template_id = #{templateId} AND user_id = #{userId}
    </select>

    <!-- 优化版：分页查询用户优惠券ID（使用覆盖索引） -->
    <select id="selectUserCouponIds" parameterType="com.blsc.marketing.coupon.bean.query.UserCouponsQuery" resultType="java.lang.Long">
        SELECT uc.id
        FROM user_coupon uc
        <if test="templateType != null">
            LEFT JOIN coupon_template ct ON uc.template_id = ct.id
        </if>
        WHERE uc.user_id = #{userId}
        <if test="appId != null and appId != ''">
            AND uc.app_id = #{appId}
        </if>
        <if test="status != null">
            AND uc.status = #{status}
        </if>
        <if test="templateType != null">
            AND ct.template_type = #{templateType}
        </if>
        ORDER BY uc.create_time DESC
        LIMIT #{offset}, #{pageSize}
    </select>

    <!-- 根据ID列表批量查询详情 -->
    <select id="selectDetailsByIds" resultMap="UserCouponDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM user_coupon uc
        LEFT JOIN coupon_template ct ON uc.template_id = ct.id
        WHERE uc.id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
        ORDER BY uc.create_time DESC
    </select>

    <!-- 批量更新优惠券状态为过期 -->
    <update id="batchUpdateToExpired" parameterType="map">
        UPDATE user_coupon
        SET status = 3, update_time = #{updateTime}
        WHERE coupon_code IN
        <foreach collection="list" item="couponCode" open="(" separator="," close=")">
            #{couponCode}
        </foreach>
    </update>

    <!-- 根据用户ID和应用ID查询优惠券（用于推荐） -->
    <select id="selectForRecommendation" parameterType="map" resultMap="UserCouponDetailResultMap">
        SELECT <include refid="Detail_Column_List"/>
        FROM user_coupon uc
        LEFT JOIN coupon_template ct ON uc.template_id = ct.id
        WHERE uc.user_id = #{userId}
          AND uc.status = 1
          AND uc.expire_time > NOW()
          AND (ct.scope_type = 1 OR (ct.scope_type = 2 AND ct.scope_value = #{appId}))
          AND (
              (ct.template_type = 1 AND (ct.min_amount IS NULL OR #{orderAmount} >= ct.min_amount))
              OR ct.template_type IN (2, 3)
          )
        ORDER BY 
            CASE ct.template_type
                WHEN 1 THEN ct.discount_amount
                WHEN 2 THEN LEAST(#{orderAmount} * (1 - ct.discount_rate), IFNULL(ct.max_discount, #{orderAmount}))
                WHEN 3 THEN LEAST(ct.discount_amount, #{orderAmount})
            END DESC,
            uc.expire_time ASC
    </select>

</mapper>
