<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.blsc.marketing.coupon.mapper.CouponTemplateMapper">

    <!-- 结果映射 -->
    <resultMap id="BaseResultMap" type="com.blsc.marketing.coupon.bean.po.CouponTemplatePO">
        <id column="id" property="id" jdbcType="BIGINT"/>
        <result column="template_name" property="templateName" jdbcType="VARCHAR"/>
        <result column="template_type" property="templateType" jdbcType="TINYINT"/>
        <result column="scope_type" property="scopeType" jdbcType="TINYINT"/>
        <result column="scope_value" property="scopeValue" jdbcType="VARCHAR"/>
        <result column="discount_amount" property="discountAmount" jdbcType="INTEGER"/>
        <result column="discount_rate" property="discountRate" jdbcType="DECIMAL"/>
        <result column="min_amount" property="minAmount" jdbcType="INTEGER"/>
        <result column="max_discount" property="maxDiscount" jdbcType="INTEGER"/>
        <result column="total_count" property="totalCount" jdbcType="INTEGER"/>
        <result column="per_user_limit" property="perUserLimit" jdbcType="INTEGER"/>
        <result column="valid_start_time" property="validStartTime" jdbcType="TIMESTAMP"/>
        <result column="valid_end_time" property="validEndTime" jdbcType="TIMESTAMP"/>
        <result column="status" property="status" jdbcType="TINYINT"/>
        <result column="description" property="description" jdbcType="VARCHAR"/>
        <result column="create_time" property="createTime" jdbcType="TIMESTAMP"/>
        <result column="update_time" property="updateTime" jdbcType="TIMESTAMP"/>
<!--        <result column="create_by" property="createBy" jdbcType="VARCHAR"/>-->
<!--        <result column="update_by" property="updateBy" jdbcType="VARCHAR"/>-->
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, template_name, template_type, scope_type, scope_value, discount_amount, 
        discount_rate, min_amount, max_discount, total_count, per_user_limit, 
        valid_start_time, valid_end_time, status, description, create_time, 
        update_time
    </sql>

    <!-- 插入优惠券模板 -->
    <insert id="insertOne" parameterType="com.blsc.marketing.coupon.bean.po.CouponTemplatePO" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO coupon_template (
            template_name, template_type, scope_type, scope_value, discount_amount,
            discount_rate, min_amount, max_discount, total_count, per_user_limit,
            valid_start_time, valid_end_time, status, description, create_time,
            update_time
        ) VALUES (
            #{templateName}, #{templateType}, #{scopeType}, #{scopeValue}, #{discountAmount},
            #{discountRate}, #{minAmount}, #{maxDiscount}, #{totalCount}, #{perUserLimit},
            #{validStartTime}, #{validEndTime}, #{status}, #{description}, #{createTime},
            #{updateTime}
        )
    </insert>

    <!-- 根据ID查询 -->
    <select id="selectById" parameterType="java.lang.Long" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_template
        WHERE id = #{id}
    </select>

    <!-- 根据ID列表查询 -->
    <select id="selectByIds" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_template
        WHERE id IN
        <foreach collection="list" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 分页查询优惠券模板 -->
    <select id="selectByCondition" parameterType="com.blsc.marketing.coupon.bean.query.CouponTemplatesQuery" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_template
        <where>
            <if test="templateName != null and templateName != ''">
                AND template_name LIKE CONCAT('%', #{templateName}, '%')
            </if>
            <if test="templateType != null">
                AND template_type = #{templateType}
            </if>
            <if test="scopeType != null">
                AND scope_type = #{scopeType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 统计查询条件下的总数 -->
    <select id="countByCondition" parameterType="com.blsc.marketing.coupon.bean.query.CouponTemplatesQuery" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM coupon_template
        <where>
            <if test="templateName != null and templateName != ''">
                AND template_name LIKE CONCAT('%', #{templateName}, '%')
            </if>
            <if test="templateType != null">
                AND template_type = #{templateType}
            </if>
            <if test="scopeType != null">
                AND scope_type = #{scopeType}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
        </where>
    </select>

    <!-- 更新优惠券模板 -->
    <update id="updateById" parameterType="com.blsc.marketing.coupon.bean.po.CouponTemplatePO">
        UPDATE coupon_template
        <set>
            <if test="templateName != null">template_name = #{templateName},</if>
            <if test="templateType != null">template_type = #{templateType},</if>
            <if test="scopeType != null">scope_type = #{scopeType},</if>
            <if test="scopeValue != null">scope_value = #{scopeValue},</if>
            <if test="discountAmount != null">discount_amount = #{discountAmount},</if>
            <if test="discountRate != null">discount_rate = #{discountRate},</if>
            <if test="minAmount != null">min_amount = #{minAmount},</if>
            <if test="maxDiscount != null">max_discount = #{maxDiscount},</if>
            <if test="totalCount != null">total_count = #{totalCount},</if>
            <if test="perUserLimit != null">per_user_limit = #{perUserLimit},</if>
            <if test="validStartTime != null">valid_start_time = #{validStartTime},</if>
            <if test="validEndTime != null">valid_end_time = #{validEndTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="description != null">description = #{description},</if>
--             <if test="updateBy != null">update_by = #{updateBy},</if>
            update_time = NOW()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 根据ID删除 -->
    <delete id="deleteById" parameterType="java.lang.Long">
        DELETE FROM coupon_template WHERE id = #{id}
    </delete>

    <!-- 查询有效的模板（用于发放） -->
    <select id="selectValidTemplates" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_template
        WHERE status = 1
          AND valid_start_time &lt;= NOW()
          AND valid_end_time &gt; NOW()
        ORDER BY create_time DESC
    </select>

    <!-- 根据范围类型和值查询模板 -->
    <select id="selectByScopeTypeAndValue" parameterType="map" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_template
        WHERE scope_type = #{scopeType}
        <if test="scopeValue != null and scopeValue != ''">
            AND scope_value = #{scopeValue}
        </if>
        AND status = 1
        AND valid_start_time &lt;= NOW()
        AND valid_end_time &gt; NOW()
        ORDER BY create_time DESC
    </select>

    <!-- 批量更新模板状态 -->
    <update id="batchUpdateStatus" parameterType="map">
        UPDATE coupon_template
        SET status = #{status}, update_time = #{updateTime}
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询即将过期的模板（用于定时任务） -->
    <select id="selectExpiringSoon" parameterType="java.time.LocalDateTime" resultMap="BaseResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM coupon_template
        WHERE status = 1
          AND valid_end_time &gt; NOW()
          AND valid_end_time &lt;= #{expireTime}
        ORDER BY valid_end_time ASC
    </select>

    <!-- 统计有效模板数量 -->
    <select id="countValidTemplates" resultType="java.lang.Integer">
        SELECT COUNT(1)
        FROM coupon_template
        WHERE status = 1
          AND valid_start_time &lt;= NOW()
          AND valid_end_time &gt; NOW()
    </select>

</mapper>
