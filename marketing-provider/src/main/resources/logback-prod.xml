<?xml version="1.0" encoding="UTF-8"?>
<configuration debug="false" scan="true" scanPeriod="1 seconds">
    <include resource="org/springframework/boot/logging/logback/base.xml"/>
    <springProperty scope="context" name="appName" source="spring.application.name"/>
    <springProperty scope="context" name="environment" source="spring.profiles.active"/>

    <!--为了防止进程退出时，内存中的数据丢失，请加上此选项-->
    <shutdownHook class="ch.qos.logback.core.hook.DelayingShutdownHook"/>

    <contextName>logback</contextName>

    <appender name="loghubAppender1" class="com.aliyun.openservices.log.logback.LoghubAppender">
        <!--必选项-->
        <!-- 账号及网络配置 -->
        <endpoint>cn-chengdu-intranet.log.aliyuncs.com</endpoint>
        <accessKeyId>LTAI5tF7ER9EjTEPp4hT3ijV</accessKeyId>
        <accessKeySecret>******************************</accessKeySecret>

        <!-- sls 项目配置 -->
        <project>zt-all-logs</project>
        <logStore>zt-prod-logs</logStore>
        <!--必选项 (end)-->

        <!-- 可选项 -->
        <!--        <topic>topic1</topic>-->
        <source>ticket-provider</source>

        <!-- 可选项 详见 '参数说明'-->
        <totalSizeInBytes>104857600</totalSizeInBytes>
        <maxBlockMs>60000</maxBlockMs>
        <ioThreadCount>8</ioThreadCount>
        <batchSizeThresholdInBytes>524288</batchSizeThresholdInBytes>
        <batchCountThreshold>4096</batchCountThreshold>
        <lingerMs>2000</lingerMs>
        <retries>10</retries>
        <baseRetryBackoffMs>100</baseRetryBackoffMs>
        <maxRetryBackoffMs>100</maxRetryBackoffMs>

        <filter class="ch.qos.logback.classic.filter.ThresholdFilter"><!-- 打印WARN,ERROR级别的日志 -->
            <level>WARN</level>
        </filter>

        <mdcFields>THREAD_ID,MDC_KEY</mdcFields>
    </appender>

    <appender name="loghubAppender2" class="com.aliyun.openservices.log.logback.LoghubAppender">
        <!--必选项-->
        <!-- 账号及网络配置 -->
        <endpoint>cn-chengdu-intranet.log.aliyuncs.com</endpoint>
        <accessKeyId>LTAI5tF7ER9EjTEPp4hT3ijV</accessKeyId>
        <accessKeySecret>******************************</accessKeySecret>

        <!-- sls 项目配置 -->
        <project>zt-all-logs</project>
        <logStore>zt-prod-logs</logStore>
        <!--必选项 (end)-->

        <!-- 可选项 -->
        <!--        <topic>topic2</topic>-->
        <source>ticket-provider</source>

        <!-- 可选项 详见 '参数说明'-->
        <totalSizeInBytes>104857600</totalSizeInBytes>
        <maxBlockMs>0</maxBlockMs>
        <ioThreadCount>8</ioThreadCount>
        <batchSizeThresholdInBytes>524288</batchSizeThresholdInBytes>
        <batchCountThreshold>4096</batchCountThreshold>
        <lingerMs>2000</lingerMs>
        <retries>10</retries>
        <baseRetryBackoffMs>100</baseRetryBackoffMs>
        <maxRetryBackoffMs>50000</maxRetryBackoffMs>

        <!-- 可选项 通过配置 encoder 的 pattern 自定义 log 的格式 -->
        <encoder>
            <pattern>%d %-5level [%thread] %logger{0}: %msg</pattern>
        </encoder>

        <!-- 可选项 设置时间格式 -->
        <timeFormat>yyyy-MM-dd'T'HH:mmZ</timeFormat>
        <!-- 可选项 设置时区 -->
        <timeZone>Asia/Shanghai</timeZone>
        <!-- 可选项 设置是否要添加 Location 字段（日志打印位置），默认为 true -->
        <includeLocation>true</includeLocation>
        <!-- 可选项 当 encoder 不为空时，是否要包含 message 字段，默认为 true。
            Optional Whether to include field message when encoder exists, defaults to true.-->
        <includeMessage>true</includeMessage>

        <filter class="ch.qos.logback.classic.filter.LevelFilter"><!-- 只打印INFO级别的日志 -->
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
        <!-- in the format of "KEY1,KEY2,KEY3" or "*" to match all keys -->
        <mdcFields>THREAD_ID,MDC_KEY</mdcFields>
        <!-- 可选项，exception 堆栈最大记录长度，超出此长度会被截断，默认值为 500 -->
        <!--        <maxThrowable>500</maxThrowable>-->
    </appender>

    <logger name="com.blsc.ticket" level="INFO">
        <appender-ref ref="loghubAppender1"/>
        <appender-ref ref="loghubAppender2"/>
    </logger>

    <root level="INFO">
        <appender-ref ref="CONSOLE"/>
    </root>

</configuration>