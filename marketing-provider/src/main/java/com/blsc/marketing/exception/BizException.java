package com.blsc.marketing.exception;

import lombok.Getter;

/**
 * @ClassName BizException
 * @Description 业务通用异常，区别于系统异常，用于处理服务之间业务交互的异常
 * <AUTHOR>
 * @Date 2024/10/19 16:31
 */
@Getter
public class BizException extends RuntimeException {

    String errorCode;

    public BizException() {

    }

    public BizException(MarketingServiceCode serviceCode) {
        this.errorCode = serviceCode.getValue();
    }

    public BizException(String message) {
        super(message);
    }

    public BizException(String message, Throwable throwable) {
        super(message, throwable);
    }

    public BizException(MarketingServiceCode serviceCode, String message) {
        super(message);
        this.errorCode = serviceCode.getValue();
    }

    public BizException(MarketingServiceCode serviceCode, String message, Throwable throwable) {
        super(message, throwable);
        this.errorCode = serviceCode.getValue();
    }

    public int getErrorCodeInt() {
        return Integer.parseInt(errorCode);
    }
}
