package com.blsc.marketing.exception;

import com.blsc.marketing.common.GrpcCommon;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;
import lombok.extern.slf4j.Slf4j;
import net.devh.boot.grpc.server.advice.GrpcAdvice;
import net.devh.boot.grpc.server.advice.GrpcExceptionHandler;

/**
 * @ClassName GrpcExceptionAdvice
 * @Description
 * <AUTHOR>
 * @Date 2024/10/23 11:12
 */

@GrpcAdvice
@Slf4j
public class GrpcExceptionAdvice {

    @GrpcExceptionHandler
    public StatusRuntimeException handleBizException(BizException e) {
        log.error("[handleBizException] 业务异常, 错误信息:{}", e.getMessage(), e);
        return GrpcCommon.convertException(e);
    }

    @GrpcExceptionHandler
    public StatusRuntimeException handleAnyException(Exception e) {
        log.error("[handleAnyException] 服务内部异常, 错误信息:{}", e.getMessage(), e);
        Status status = Status.INTERNAL.withDescription("营销服务内部异常").withCause(e);
        return status.asRuntimeException();
    }

}
