package com.blsc.marketing.coupon.bean.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 创建优惠券模板DTO
 */
@Data
public class CreateCouponTemplateDTO {
    
    @NotBlank(message = "模板名称不能为空")
    private String templateName;
    
    @NotNull(message = "优惠券类型不能为空")
    private Integer templateType;
    
    @NotNull(message = "使用范围类型不能为空")
    private Integer scopeType;
    
    private String scopeValue;
    
    private Integer discountAmount;
    
    private Double discountRate;
    
    private Integer minAmount;
    
    private Integer maxDiscount;
    
    @NotNull(message = "发放总数量不能为空")
    @Positive(message = "发放总数量必须大于0")
    private Integer totalCount;
    
    @NotNull(message = "每用户限领数量不能为空")
    @Positive(message = "每用户限领数量必须大于0")
    private Integer perUserLimit;
    
    @NotNull(message = "有效期开始时间不能为空")
    private LocalDateTime validStartTime;
    
    @NotNull(message = "有效期结束时间不能为空")
    private LocalDateTime validEndTime;
    
    private String description;
    
    @NotBlank(message = "创建人不能为空")
    private String createBy;
}
