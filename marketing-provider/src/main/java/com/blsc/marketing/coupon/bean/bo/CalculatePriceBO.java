package com.blsc.marketing.coupon.bean.bo;

import lombok.Data;

import java.util.List;

/**
 * 计算营销价格业务对象
 */
@Data
public class CalculatePriceBO {
    
    /**
     * 原始金额(分)
     */
    private Integer originalAmount;
    
    /**
     * 总优惠金额(分)
     */
    private Integer totalDiscountAmount;
    
    /**
     * 最终金额(分)
     */
    private Integer finalAmount;
    
    /**
     * 使用的优惠券详情
     */
    private List<CouponDiscountDetailBO> couponDetails;
    
    /**
     * 活动优惠金额(分) - 预留字段
     */
    private Integer activityDiscountAmount = 0;
    
    /**
     * 计算成功标识
     */
    private Boolean success = true;
    
    /**
     * 错误信息
     */
    private String message;

    /**
     * 是否为预计算
     */
    private Boolean preCalculate = false;
}
