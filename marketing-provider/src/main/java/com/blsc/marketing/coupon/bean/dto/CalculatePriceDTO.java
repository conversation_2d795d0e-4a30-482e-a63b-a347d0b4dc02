package com.blsc.marketing.coupon.bean.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

import java.util.List;

/**
 * 计算营销价格DTO
 */
@Data
public class CalculatePriceDTO {
    
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    @NotBlank(message = "应用ID不能为空")
    private String appId;
    
    @NotNull(message = "原始金额不能为空")
    @Positive(message = "原始金额必须大于0")
    private Integer originalAmount;
    
    private List<String> couponCodes;
    
    private String productId;
    
    private String orderId;
    
    /**
     * 是否只是预计算（不实际使用优惠券）
     */
    private Boolean preCalculate = true;
}
