package com.blsc.marketing.coupon.bean;

/**
 * 优惠券相关常量定义
 */
public class CouponConstants {
    
    /**
     * 优惠券类型
     */
    public static class CouponType {
        /** 满减券 */
        public static final int FULL_REDUCTION = 1;
        /** 折扣券 */
        public static final int DISCOUNT = 2;
        /** 立减券 */
        public static final int DIRECT_REDUCTION = 3;
    }
    
    /**
     * 使用范围类型
     */
    public static class ScopeType {
        /** 平台级 */
        public static final int PLATFORM = 1;
        /** 应用级 */
        public static final int APPLICATION = 2;
        /** 商品级 */
        public static final int PRODUCT = 3;
    }
    
    /**
     * 优惠券状态
     */
    public static class CouponStatus {
        /** 未使用 */
        public static final int UNUSED = 1;
        /** 已使用 */
        public static final int USED = 2;
        /** 已过期 */
        public static final int EXPIRED = 3;
    }
    
    /**
     * 模板状态
     */
    public static class TemplateStatus {
        /** 禁用 */
        public static final int DISABLED = 0;
        /** 启用 */
        public static final int ENABLED = 1;
    }
    
    /**
     * 发放类型
     */
    public static class IssueType {
        /** 用户主动领取 */
        public static final int USER_CLAIM = 1;
        /** 系统定时发放 */
        public static final int SYSTEM_SCHEDULE = 2;
        /** 活动发放 */
        public static final int ACTIVITY_ISSUE = 3;
    }
    
    /**
     * Redis缓存Key前缀
     */
    public static class CacheKey {
        /** 优惠券模板缓存前缀 */
        public static final String COUPON_TEMPLATE_PREFIX = "coupon:template:";
        /** 用户优惠券缓存前缀 */
        public static final String USER_COUPON_PREFIX = "coupon:user:";
        /** 优惠券已发放数量缓存前缀 */
        public static final String COUPON_ISSUED_COUNT_PREFIX = "coupon:issued:count:";
        /** 用户已领取数量缓存前缀 */
        public static final String USER_CLAIMED_COUNT_PREFIX = "coupon:user:claimed:";
        /** 分布式锁前缀 */
        public static final String LOCK_PREFIX = "coupon:lock:";
    }
    
    /**
     * 缓存过期时间（秒）
     */
    public static class CacheExpire {
        /** 优惠券模板缓存过期时间：1小时 */
        public static final int COUPON_TEMPLATE_EXPIRE = 3600;
        /** 用户优惠券缓存过期时间：30分钟 */
        public static final int USER_COUPON_EXPIRE = 1800;
        /** 发放数量缓存过期时间：24小时 */
        public static final int ISSUED_COUNT_EXPIRE = 86400;
        /** 分布式锁过期时间：30秒 */
        public static final int LOCK_EXPIRE = 30;
    }
    
    /**
     * 业务限制常量
     */
    public static class BusinessLimit {
        /** 单次查询最大数量 */
        public static final int MAX_QUERY_SIZE = 1000;
        /** 优惠券码长度 */
        public static final int COUPON_CODE_LENGTH = 16;
        /** 最大折扣率 */
        public static final double MAX_DISCOUNT_RATE = 0.99;
        /** 最小折扣率 */
        public static final double MIN_DISCOUNT_RATE = 0.01;
    }
    
    /**
     * 错误消息
     */
    public static class ErrorMessage {
        public static final String TEMPLATE_NOT_FOUND = "优惠券模板不存在";
        public static final String TEMPLATE_DISABLED = "优惠券模板已禁用";
        public static final String TEMPLATE_EXPIRED = "优惠券模板已过期";
        public static final String COUPON_NOT_FOUND = "优惠券不存在";
        public static final String COUPON_USED = "优惠券已使用";
        public static final String COUPON_EXPIRED = "优惠券已过期";
        public static final String INSUFFICIENT_STOCK = "优惠券库存不足";
        public static final String EXCEED_USER_LIMIT = "超出用户领取限制";
        public static final String AMOUNT_NOT_MEET = "订单金额不满足使用条件";
        public static final String SCOPE_NOT_MATCH = "优惠券使用范围不匹配";
    }
}
