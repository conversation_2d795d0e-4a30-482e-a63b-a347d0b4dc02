package com.blsc.marketing.coupon.mapper;

import com.blsc.marketing.coupon.bean.po.CouponIssueRecordPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 优惠券发放记录数据访问层
 */
@Mapper
public interface CouponIssueRecordMapper {
    
    /**
     * 插入发放记录
     * 
     * @param record 发放记录
     * @return 影响行数
     */
    int insert(CouponIssueRecordPO record);
    
    /**
     * 批量插入发放记录
     * 
     * @param records 发放记录列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<CouponIssueRecordPO> records);
    
    /**
     * 根据ID查询发放记录
     * 
     * @param id 记录ID
     * @return 发放记录
     */
    CouponIssueRecordPO selectById(@Param("id") Long id);
    
    /**
     * 根据优惠券码查询发放记录
     * 
     * @param couponCode 优惠券码
     * @return 发放记录
     */
    CouponIssueRecordPO selectByCouponCode(@Param("couponCode") String couponCode);
    
    /**
     * 根据用户ID查询发放记录
     * 
     * @param userId 用户ID
     * @param pageSize 分页大小
     * @param offset 偏移量
     * @return 发放记录列表
     */
    List<CouponIssueRecordPO> selectByUserId(@Param("userId") String userId,
                                            @Param("pageSize") Integer pageSize,
                                            @Param("offset") Integer offset);
    
    /**
     * 根据模板ID查询发放记录
     * 
     * @param templateId 模板ID
     * @param pageSize 分页大小
     * @param offset 偏移量
     * @return 发放记录列表
     */
    List<CouponIssueRecordPO> selectByTemplateId(@Param("templateId") Long templateId,
                                                @Param("pageSize") Integer pageSize,
                                                @Param("offset") Integer offset);
    
    /**
     * 根据应用ID查询发放记录
     * 
     * @param appId 应用ID
     * @param pageSize 分页大小
     * @param offset 偏移量
     * @return 发放记录列表
     */
    List<CouponIssueRecordPO> selectByAppId(@Param("appId") String appId,
                                           @Param("pageSize") Integer pageSize,
                                           @Param("offset") Integer offset);
    
    /**
     * 根据发放类型查询发放记录
     * 
     * @param issueType 发放类型
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageSize 分页大小
     * @param offset 偏移量
     * @return 发放记录列表
     */
    List<CouponIssueRecordPO> selectByIssueType(@Param("issueType") Integer issueType,
                                               @Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime,
                                               @Param("pageSize") Integer pageSize,
                                               @Param("offset") Integer offset);
    
    /**
     * 统计用户在指定模板下的发放次数
     * 
     * @param userId 用户ID
     * @param templateId 模板ID
     * @return 发放次数
     */
    Integer countUserIssueRecords(@Param("userId") String userId, 
                                 @Param("templateId") Long templateId);
    
    /**
     * 统计模板的总发放次数
     * 
     * @param templateId 模板ID
     * @return 总发放次数
     */
    Integer countTemplateIssueRecords(@Param("templateId") Long templateId);
    
    /**
     * 根据时间范围统计发放记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param templateId 模板ID（可选）
     * @param appId 应用ID（可选）
     * @return 发放次数
     */
    Integer countIssueRecordsByTimeRange(@Param("startTime") LocalDateTime startTime,
                                        @Param("endTime") LocalDateTime endTime,
                                        @Param("templateId") Long templateId,
                                        @Param("appId") String appId);
    
    /**
     * 查询指定时间范围内的发放记录
     * 
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param pageSize 分页大小
     * @param offset 偏移量
     * @return 发放记录列表
     */
    List<CouponIssueRecordPO> selectByTimeRange(@Param("startTime") LocalDateTime startTime,
                                               @Param("endTime") LocalDateTime endTime,
                                               @Param("pageSize") Integer pageSize,
                                               @Param("offset") Integer offset);
}
