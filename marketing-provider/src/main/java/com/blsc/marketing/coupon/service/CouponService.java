package com.blsc.marketing.coupon.service;

import com.blsc.marketing.coupon.bean.bo.PageBO;
import com.blsc.marketing.coupon.bean.bo.UserCouponBO;
import com.blsc.marketing.coupon.bean.bo.UseCouponBO;
import com.blsc.marketing.coupon.bean.bo.ValidateCouponBO;
import com.blsc.marketing.coupon.bean.dto.ClaimCouponDTO;
import com.blsc.marketing.coupon.bean.dto.QueryUsageRecordsDTO;
import com.blsc.marketing.coupon.bean.dto.QueryUserCouponsDTO;
import com.blsc.marketing.coupon.bean.dto.UseCouponDTO;
import com.blsc.marketing.coupon.bean.dto.ValidateCouponDTO;

import java.util.List;

/**
 * 优惠券服务接口
 */
public interface CouponService {
    
    /**
     * 用户领取优惠券
     *
     * @param dto 领取DTO
     * @return 优惠券码
     */
    String claimCoupon(ClaimCouponDTO dto);

    /**
     * 查询用户优惠券列表
     *
     * @param dto 查询DTO
     * @return 分页结果
     */
    PageBO<UserCouponBO> queryUserCoupons(QueryUserCouponsDTO dto);
    
    /**
     * 根据优惠券码查询优惠券详情
     * 
     * @param couponCode 优惠券码
     * @return 优惠券详情
     */
    UserCouponBO getCouponByCode(String couponCode);
    
    /**
     * 根据优惠券码列表查询优惠券详情
     * 
     * @param couponCodes 优惠券码列表
     * @return 优惠券详情列表
     */
    List<UserCouponBO> getCouponsByCodes(List<String> couponCodes);
    
    /**
     * 验证优惠券可用性
     *
     * @param dto 验证DTO
     * @return 验证结果
     */
    ValidateCouponBO validateCoupon(ValidateCouponDTO dto);
    
    /**
     * 使用优惠券
     *
     * @param dto 使用DTO
     * @return 使用结果
     */
    UseCouponBO useCoupon(UseCouponDTO dto);
    
    /**
     * 查询用户可用的优惠券
     * 
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 可用优惠券列表
     */
    List<UserCouponBO> getAvailableCoupons(String userId, String appId);
    
    /**
     * 批量过期优惠券
     * 
     * @return 过期数量
     */
    int expireCoupons();
    
    /**
     * 检查优惠券是否属于指定用户
     * 
     * @param couponCode 优惠券码
     * @param userId 用户ID
     * @return 是否属于用户
     */
    boolean isCouponBelongsToUser(String couponCode, String userId);
    
    /**
     * 批量发放优惠券
     * 
     * @param templateId 模板ID
     * @param userIds 用户ID列表
     * @param appId 应用ID
     * @return 发放成功的优惠券码列表
     */
    List<String> batchIssueCoupons(Long templateId, List<String> userIds, String appId);
    
    /**
     * 撤销优惠券（仅限未使用状态）
     * 
     * @param couponCode 优惠券码
     * @param reason 撤销原因
     * @return 是否成功
     */
    Boolean revokeCoupon(String couponCode, String reason);
    
    /**
     * 查询用户在指定模板下已领取的优惠券数量
     * 
     * @param userId 用户ID
     * @param templateId 模板ID
     * @return 已领取数量
     */
    Integer getUserClaimedCount(String userId, Long templateId);
    
    /**
     * 查询模板已发放的优惠券数量
     * 
     * @param templateId 模板ID
     * @return 已发放数量
     */
    Integer getTemplateIssuedCount(Long templateId);
    
    /**
     * 预发放优惠券（检查但不实际发放）
     *
     * @param dto 领取DTO
     * @return 预发放结果
     */
    Boolean preIssueCoupon(ClaimCouponDTO dto);
}
