package com.blsc.marketing.coupon.bean.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * 使用优惠券DTO
 */
@Data
public class UseCouponDTO {
    
    @NotBlank(message = "优惠券码不能为空")
    private String couponCode;
    
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    @NotBlank(message = "应用ID不能为空")
    private String appId;
    
    @NotBlank(message = "订单ID不能为空")
    private String orderId;
    
    @NotNull(message = "原始金额不能为空")
    @Positive(message = "原始金额必须大于0")
    private Integer originalAmount;
    
    private String productId;
}
