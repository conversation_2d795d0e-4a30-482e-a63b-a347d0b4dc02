package com.blsc.marketing.coupon.mapper;

import com.blsc.marketing.coupon.bean.po.CouponTemplatePO;
import com.blsc.marketing.coupon.bean.query.CouponTemplatesQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 优惠券模板数据访问层
 */
@Mapper
public interface CouponTemplateMapper {
    
    /**
     * 插入优惠券模板
     * 
     * @param template 优惠券模板
     * @return 影响行数
     */
    int insertOne(CouponTemplatePO template);
    
    /**
     * 根据ID查询优惠券模板
     * 
     * @param id 模板ID
     * @return 优惠券模板
     */
    CouponTemplatePO selectById(@Param("id") Long id);
    
    /**
     * 根据ID列表查询优惠券模板
     * 
     * @param ids ID列表
     * @return 优惠券模板列表
     */
    List<CouponTemplatePO> selectByIds(@Param("list") List<Long> ids);
    
    /**
     * 根据条件分页查询优惠券模板
     *
     * @param query 查询条件
     * @return 优惠券模板列表
     */
    List<CouponTemplatePO> selectByCondition(CouponTemplatesQuery query);

    /**
     * 统计查询条件下的总数
     *
     * @param query 查询条件
     * @return 总数
     */
    Integer countByCondition(CouponTemplatesQuery query);
    
    /**
     * 更新优惠券模板
     * 
     * @param template 优惠券模板
     * @return 影响行数
     */
    int updateById(CouponTemplatePO template);
    
    /**
     * 根据ID删除优惠券模板
     * 
     * @param id 模板ID
     * @return 影响行数
     */
    int deleteById(@Param("id") Long id);
    
    /**
     * 查询有效的模板（用于发放）
     * 
     * @return 有效模板列表
     */
    List<CouponTemplatePO> selectValidTemplates();
    
    /**
     * 根据范围类型和值查询模板
     * 
     * @param scopeType 范围类型
     * @param scopeValue 范围值
     * @return 模板列表
     */
    List<CouponTemplatePO> selectByScopeTypeAndValue(@Param("scopeType") Integer scopeType, 
                                                     @Param("scopeValue") String scopeValue);
    
    /**
     * 批量更新模板状态
     *
     * @param ids 模板ID列表
     * @param status 状态
     * @param updateTime 更新时间
     * @param updateBy 更新人
     * @return 影响行数
     */
    int batchUpdateStatus(@Param("ids") List<Long> ids,
                         @Param("status") Integer status,
                         @Param("updateTime") LocalDateTime updateTime,
                         @Param("updateBy") String updateBy);

    /**
     * 统计有效模板数量
     *
     * @return 有效模板数量
     */
    Integer countValidTemplates();
    
    /**
     * 查询即将过期的模板（用于定时任务）
     * 
     * @param expireTime 过期时间
     * @return 即将过期的模板列表
     */
    List<CouponTemplatePO> selectExpiringSoon(@Param("expireTime") LocalDateTime expireTime);
}
