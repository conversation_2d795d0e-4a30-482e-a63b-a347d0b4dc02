package com.blsc.marketing.coupon.bean.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 领取优惠券DTO
 */
@Data
public class ClaimCouponDTO {
    
    @NotNull(message = "模板ID不能为空")
    private Long templateId;
    
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    @NotBlank(message = "应用ID不能为空")
    private String appId;
}
