package com.blsc.marketing.coupon.util;

import com.blsc.marketing.coupon.bean.bo.CouponTemplateBO;
import com.blsc.marketing.coupon.bean.bo.UserCouponBO;
import com.blsc.marketing.coupon.bean.dto.CreateCouponTemplateDTO;
import com.blsc.marketing.coupon.bean.po.CouponIssueRecordPO;
import com.blsc.marketing.coupon.bean.po.CouponTemplatePO;
import com.blsc.marketing.coupon.bean.po.CouponUsageRecordPO;
import com.blsc.marketing.coupon.bean.po.UserCouponPO;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 实体类转换工具
 */
public class BeanConverter {
    
    /**
     * CouponTemplatePO 转 CouponTemplateBO
     */
    public static CouponTemplateBO convertToBO(CouponTemplatePO po) {
        if (po == null) {
            return null;
        }
        
        CouponTemplateBO bo = new CouponTemplateBO();
        bo.setId(po.getId());
        bo.setTemplateName(po.getTemplateName());
        bo.setTemplateType(po.getTemplateType());
        bo.setScopeType(po.getScopeType());
        bo.setScopeValue(po.getScopeValue());
        bo.setDiscountAmount(po.getDiscountAmount());
        bo.setDiscountRate(po.getDiscountRate());
        bo.setMinAmount(po.getMinAmount());
        bo.setMaxDiscount(po.getMaxDiscount());
        bo.setTotalCount(po.getTotalCount());
        bo.setPerUserLimit(po.getPerUserLimit());
        bo.setValidStartTime(po.getValidStartTime());
        bo.setValidEndTime(po.getValidEndTime());
        bo.setStatus(po.getStatus());
        bo.setDescription(po.getDescription());
        bo.setCreateTime(po.getCreateTime());
        bo.setUpdateTime(po.getUpdateTime());
        // createBy和updateBy字段不在数据库表中
        // bo.setCreateBy(po.getCreateBy());
        // bo.setUpdateBy(po.getUpdateBy());
        
        return bo;
    }
    
    /**
     * CouponTemplateBO 转 CouponTemplatePO
     */
    public static CouponTemplatePO convertToPO(CouponTemplateBO bo) {
        if (bo == null) {
            return null;
        }
        
        CouponTemplatePO po = new CouponTemplatePO();
        po.setId(bo.getId());
        po.setTemplateName(bo.getTemplateName());
        po.setTemplateType(bo.getTemplateType());
        po.setScopeType(bo.getScopeType());
        po.setScopeValue(bo.getScopeValue());
        po.setDiscountAmount(bo.getDiscountAmount());
        po.setDiscountRate(bo.getDiscountRate());
        po.setMinAmount(bo.getMinAmount());
        po.setMaxDiscount(bo.getMaxDiscount());
        po.setTotalCount(bo.getTotalCount());
        po.setPerUserLimit(bo.getPerUserLimit());
        po.setValidStartTime(bo.getValidStartTime());
        po.setValidEndTime(bo.getValidEndTime());
        po.setStatus(bo.getStatus());
        po.setDescription(bo.getDescription());
        po.setCreateTime(bo.getCreateTime());
        po.setUpdateTime(bo.getUpdateTime());
        // createBy和updateBy字段不在数据库表中
        // po.setCreateBy(bo.getCreateBy());
        // po.setUpdateBy(bo.getUpdateBy());
        
        return po;
    }
    
    /**
     * CreateCouponTemplateDTO 转 CouponTemplatePO
     */
    public static CouponTemplatePO convertToPO(CreateCouponTemplateDTO request) {
        if (request == null) {
            return null;
        }
        
        CouponTemplatePO po = new CouponTemplatePO();
        po.setTemplateName(request.getTemplateName());
        po.setTemplateType(request.getTemplateType());
        po.setScopeType(request.getScopeType());
        po.setScopeValue(request.getScopeValue());
        po.setDiscountAmount(request.getDiscountAmount());
        po.setDiscountRate(request.getDiscountRate());
        po.setMinAmount(request.getMinAmount());
        po.setMaxDiscount(request.getMaxDiscount());
        po.setTotalCount(request.getTotalCount());
        po.setPerUserLimit(request.getPerUserLimit());
        po.setValidStartTime(request.getValidStartTime());
        po.setValidEndTime(request.getValidEndTime());
        po.setStatus(1); // 默认启用
        po.setDescription(request.getDescription());
        po.setCreateTime(LocalDateTime.now());
        po.setUpdateTime(LocalDateTime.now());
        // createBy和updateBy字段不在数据库表中
        // po.setCreateBy(request.getCreateBy());
        // po.setUpdateBy(request.getCreateBy());
        
        return po;
    }
    
    /**
     * UserCouponPO 转 UserCouponBO
     */
    public static UserCouponBO convertToBO(UserCouponPO po) {
        if (po == null) {
            return null;
        }
        
        UserCouponBO bo = new UserCouponBO();
        bo.setId(po.getId());
        bo.setCouponCode(po.getCouponCode());
        bo.setTemplateId(po.getTemplateId());
        bo.setUserId(po.getUserId());
        bo.setAppId(po.getAppId());
        bo.setStatus(po.getStatus());
        bo.setUsedTime(po.getUsedTime());
        bo.setUsedOrderId(po.getUsedOrderId());
        bo.setExpireTime(po.getExpireTime());
        bo.setCreateTime(po.getCreateTime());
        bo.setUpdateTime(po.getUpdateTime());
        
        return bo;
    }
    
    /**
     * UserCouponBO 转 UserCouponPO
     */
    public static UserCouponPO convertToPO(UserCouponBO bo) {
        if (bo == null) {
            return null;
        }
        
        UserCouponPO po = new UserCouponPO();
        po.setId(bo.getId());
        po.setCouponCode(bo.getCouponCode());
        po.setTemplateId(bo.getTemplateId());
        po.setUserId(bo.getUserId());
        po.setAppId(bo.getAppId());
        po.setStatus(bo.getStatus());
        po.setUsedTime(bo.getUsedTime());
        po.setUsedOrderId(bo.getUsedOrderId());
        po.setExpireTime(bo.getExpireTime());
        po.setCreateTime(bo.getCreateTime());
        po.setUpdateTime(bo.getUpdateTime());
        
        return po;
    }
    
    /**
     * 创建用户优惠券PO
     */
    public static UserCouponPO createUserCouponPO(String couponCode, Long templateId, 
                                                  String userId, String appId, 
                                                  LocalDateTime expireTime) {
        UserCouponPO po = new UserCouponPO();
        po.setCouponCode(couponCode);
        po.setTemplateId(templateId);
        po.setUserId(userId);
        po.setAppId(appId);
        po.setStatus(1); // 未使用
        po.setExpireTime(expireTime);
        po.setCreateTime(LocalDateTime.now());
        po.setUpdateTime(LocalDateTime.now());
        
        return po;
    }
    
    /**
     * 创建优惠券发放记录PO
     */
    public static CouponIssueRecordPO createIssueRecordPO(Long templateId, String userId, 
                                                          String appId, String couponCode, 
                                                          Integer issueType, LocalDateTime expireTime) {
        CouponIssueRecordPO po = new CouponIssueRecordPO();
        po.setTemplateId(templateId);
        po.setUserId(userId);
        po.setAppId(appId);
        po.setCouponCode(couponCode);
        po.setIssueType(issueType);
        po.setIssueTime(LocalDateTime.now());
        po.setExpireTime(expireTime);
        po.setCreateTime(LocalDateTime.now());
        
        return po;
    }
    
    /**
     * 创建优惠券使用记录PO
     */
    public static CouponUsageRecordPO createUsageRecordPO(String couponCode, Long templateId,
                                                          String userId, String appId, String orderId,
                                                          Integer originalAmount, Integer discountAmount,
                                                          Integer finalAmount) {
        CouponUsageRecordPO po = new CouponUsageRecordPO();
        po.setCouponCode(couponCode);
        po.setTemplateId(templateId);
        po.setUserId(userId);
        po.setAppId(appId);
        po.setOrderId(orderId);
        po.setOriginalAmount(originalAmount);
        po.setDiscountAmount(discountAmount);
        po.setFinalAmount(finalAmount);
        po.setUsedTime(LocalDateTime.now());
        po.setCreateTime(LocalDateTime.now());
        
        return po;
    }
    
    /**
     * 批量转换 CouponTemplatePO 列表
     */
    public static List<CouponTemplateBO> convertToBOList(List<CouponTemplatePO> poList) {
        if (poList == null) {
            return null;
        }
        return poList.stream().map(BeanConverter::convertToBO).collect(Collectors.toList());
    }
    
    /**
     * 批量转换 UserCouponPO 列表
     */
    public static List<UserCouponBO> convertToUserCouponBOList(List<UserCouponPO> poList) {
        if (poList == null) {
            return null;
        }
        return poList.stream().map(BeanConverter::convertToBO).collect(Collectors.toList());
    }
}
