package com.blsc.marketing.coupon.mapper;

import com.blsc.marketing.coupon.bean.bo.UserCouponBO;
import com.blsc.marketing.coupon.bean.po.UserCouponPO;
import com.blsc.marketing.coupon.bean.query.UserCouponsQuery;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户优惠券数据访问层
 */
@Mapper
public interface UserCouponMapper {
    
    /**
     * 插入用户优惠券
     * 
     * @param userCoupon 用户优惠券
     * @return 影响行数
     */
    int insert(UserCouponPO userCoupon);
    
    /**
     * 批量插入用户优惠券
     * 
     * @param userCoupons 用户优惠券列表
     * @return 影响行数
     */
    int batchInsert(@Param("list") List<UserCouponPO> userCoupons);
    
    /**
     * 根据优惠券码查询用户优惠券
     * 
     * @param couponCode 优惠券码
     * @return 用户优惠券
     */
    UserCouponPO selectByCouponCode(@Param("couponCode") String couponCode);
    
    /**
     * 根据优惠券码列表查询用户优惠券
     * 
     * @param couponCodes 优惠券码列表
     * @return 用户优惠券列表
     */
    List<UserCouponPO> selectByCouponCodes(@Param("list") List<String> couponCodes);
    
    /**
     * 根据优惠券码查询用户优惠券详情（包含模板信息）
     * 
     * @param couponCode 优惠券码
     * @return 用户优惠券业务对象
     */
    UserCouponBO selectDetailByCouponCode(@Param("couponCode") String couponCode);
    
    /**
     * 根据优惠券码列表查询用户优惠券详情（包含模板信息）
     * 
     * @param couponCodes 优惠券码列表
     * @return 用户优惠券业务对象列表
     */
    List<UserCouponBO> selectDetailByCouponCodes(@Param("list") List<String> couponCodes);
    
    /**
     * 根据条件分页查询用户优惠券
     *
     * @param query 查询条件
     * @return 用户优惠券列表
     */
    List<UserCouponBO> selectByCondition(UserCouponsQuery query);

    /**
     * 统计查询条件下的总数
     *
     * @param query 查询条件
     * @return 总数
     */
    Integer countByCondition(UserCouponsQuery query);
    
    /**
     * 更新用户优惠券状态
     * 
     * @param couponCode 优惠券码
     * @param status 状态
     * @param usedTime 使用时间
     * @param usedOrderId 使用的订单ID
     * @param updateTime 更新时间
     * @return 影响行数
     */
    int updateStatus(@Param("couponCode") String couponCode,
                    @Param("status") Integer status,
                    @Param("usedTime") LocalDateTime usedTime,
                    @Param("usedOrderId") String usedOrderId,
                    @Param("updateTime") LocalDateTime updateTime);
    
    /**
     * 统计用户已领取的优惠券数量
     * 
     * @param userId 用户ID
     * @param templateId 模板ID
     * @return 已领取数量
     */
    Integer countUserClaimedCoupons(@Param("userId") String userId, 
                                   @Param("templateId") Long templateId);
    
    /**
     * 统计模板已发放的优惠券数量
     * 
     * @param templateId 模板ID
     * @return 已发放数量
     */
    Integer countIssuedCoupons(@Param("templateId") Long templateId);
    
    /**
     * 查询用户可用的优惠券
     * 
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 可用优惠券列表
     */
    List<UserCouponBO> selectAvailableCoupons(@Param("userId") String userId, 
                                             @Param("appId") String appId);
    
    /**
     * 查询即将过期的优惠券
     * 
     * @param expireTime 过期时间
     * @return 即将过期的优惠券列表
     */
    List<UserCouponPO> selectExpiringSoon(@Param("expireTime") LocalDateTime expireTime);
    
    /**
     * 批量更新优惠券状态为过期
     *
     * @param couponCodes 优惠券码列表
     * @param updateTime 更新时间
     * @return 影响行数
     */
    int batchUpdateToExpired(@Param("list") List<String> couponCodes,
                            @Param("updateTime") LocalDateTime updateTime);

    // ===== 优化后的查询方法 =====

    /**
     * 优化版：批量查询即将过期的优惠券（利用 idx_expire_status 索引）
     *
     * @param expireTime 过期时间
     * @param batchSize 批量大小
     * @return 即将过期的优惠券列表
     */
    List<UserCouponPO> selectExpiringSoonOptimized(@Param("expireTime") LocalDateTime expireTime,
                                                   @Param("batchSize") Integer batchSize);

    /**
     * 优化版：查询用户在指定应用下的可用优惠券（利用 idx_user_status_expire 索引）
     *
     * @param userId 用户ID
     * @param appId 应用ID
     * @param currentTime 当前时间
     * @return 可用优惠券列表
     */
    List<UserCouponBO> selectAvailableCouponsOptimized(@Param("userId") String userId,
                                                       @Param("appId") String appId,
                                                       @Param("currentTime") LocalDateTime currentTime);

    /**
     * 优化版：统计用户在指定模板下的领取数量（利用 idx_template_user 索引）
     *
     * @param templateId 模板ID
     * @param userId 用户ID
     * @return 已领取数量
     */
    Integer countUserClaimedCouponsOptimized(@Param("templateId") Long templateId,
                                           @Param("userId") String userId);

    /**
     * 优化版：分页查询用户优惠券ID（使用覆盖索引）
     *
     * @param query 查询条件
     * @return 优惠券ID列表
     */
    List<Long> selectUserCouponIds(UserCouponsQuery query);

    /**
     * 根据ID列表批量查询详情
     *
     * @param ids ID列表
     * @return 用户优惠券详情列表
     */
    List<UserCouponBO> selectDetailsByIds(@Param("ids") List<Long> ids);
    
    /**
     * 根据用户ID和应用ID查询优惠券（用于推荐）
     * 
     * @param userId 用户ID
     * @param appId 应用ID
     * @param orderAmount 订单金额
     * @return 可用优惠券列表
     */
    List<UserCouponBO> selectForRecommendation(@Param("userId") String userId,
                                              @Param("appId") String appId,
                                              @Param("orderAmount") Integer orderAmount);
}
