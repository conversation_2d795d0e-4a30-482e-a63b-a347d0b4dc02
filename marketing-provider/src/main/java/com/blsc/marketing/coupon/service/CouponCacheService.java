package com.blsc.marketing.coupon.service;

import com.blsc.marketing.coupon.bean.bo.CouponTemplateBO;
import com.blsc.marketing.coupon.bean.bo.UserCouponBO;

import java.util.List;

/**
 * 优惠券缓存服务接口，层级较低，其他Service均可以调用该接口
 *
 * 优化说明：
 * 1. 移除优惠券模板缓存 - 模板数据变化不频繁，直接查询数据库即可
 * 2. 移除用户优惠券列表缓存 - 避免缓存一致性问题，按需查询
 * 3. 只保留计数相关的缓存 - 这是高频访问且对性能影响较大的数据
 */
public interface CouponCacheService {
    
    /**
     * 增加模板已发放数量
     *
     * @param templateId 模板ID
     * @param count 增加数量
     * @return 增加后的总数量
     */
    Long incrementIssuedCount(Long templateId, Long count);
    
    /**
     * 获取模板已发放数量
     * 
     * @param templateId 模板ID
     * @return 已发放数量
     */
    Long getIssuedCount(Long templateId);
    
    /**
     * 设置模板已发放数量
     * 
     * @param templateId 模板ID
     * @param count 发放数量
     */
    void setIssuedCount(Long templateId, Long count);
    
    /**
     * 增加用户已领取数量
     *
     * @param userId 用户ID
     * @param templateId 模板ID
     * @param count 增加数量
     * @return 增加后的总数量
     */
    Long incrementUserClaimedCount(String userId, Long templateId, Long count);
    
    /**
     * 获取用户已领取数量
     * 
     * @param userId 用户ID
     * @param templateId 模板ID
     * @return 已领取数量
     */
    Long getUserClaimedCount(String userId, Long templateId);
    
    /**
     * 设置用户已领取数量
     *
     * @param userId 用户ID
     * @param templateId 模板ID
     * @param count 领取数量
     */
    void setUserClaimedCount(String userId, Long templateId, Long count);

    /**
     * 清除用户在指定模板下的领取数量缓存
     *
     * @param userId 用户ID
     * @param templateId 模板ID
     */
    void evictUserClaimedCount(String userId, Long templateId);

    /**
     * 清除模板的发放数量缓存
     *
     * @param templateId 模板ID
     */
    void evictIssuedCount(Long templateId);

    // ===== 分布式锁相关方法 =====

    /**
     * 获取分布式锁
     *
     * @param lockKey 锁键
     * @param expireSeconds 过期时间（秒）
     * @return 是否获取成功
     */
    Boolean tryLock(String lockKey, Integer expireSeconds);
    
    /**
     * 释放分布式锁
     * 
     * @param lockKey 锁键
     */
    void releaseLock(String lockKey);
    
    /**
     * 预热缓存
     * 
     * @param templateIds 模板ID列表
     */
    void warmUpCache(List<Long> templateIds);
    
    /**
     * 清空所有缓存
     */
    void clearAllCache();
    
    /**
     * 检查缓存是否存在
     * 
     * @param key 缓存键
     * @return 是否存在
     */
    Boolean exists(String key);
    
    /**
     * 设置缓存过期时间
     * 
     * @param key 缓存键
     * @param seconds 过期时间（秒）
     */
    void expire(String key, Integer seconds);
}
