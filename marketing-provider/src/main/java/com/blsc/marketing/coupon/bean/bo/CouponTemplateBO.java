package com.blsc.marketing.coupon.bean.bo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 优惠券模板业务对象
 */
@Data
public class CouponTemplateBO {
    
    /**
     * 模板ID
     */
    private Long id;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 优惠券类型：1-满减券，2-折扣券，3-立减券
     */
    private Integer templateType;
    
    /**
     * 使用范围类型：1-平台级，2-应用级，3-商品级
     */
    private Integer scopeType;
    
    /**
     * 范围值：应用级存应用ID，商品级存商品ID列表(JSON)
     */
    private String scopeValue;
    
    /**
     * 优惠金额(分)，满减券和立减券使用
     */
    private Integer discountAmount;
    
    /**
     * 折扣率(0.01-0.99)，折扣券使用
     */
    private Double discountRate;
    
    /**
     * 最低消费金额(分)，满减券使用
     */
    private Integer minAmount;
    
    /**
     * 最大优惠金额(分)，折扣券使用
     */
    private Integer maxDiscount;
    
    /**
     * 发放总数量
     */
    private Integer totalCount;
    
    /**
     * 已发放数量
     */
    private Integer issuedCount;
    
    /**
     * 剩余数量
     */
    private Integer remainingCount;
    
    /**
     * 每用户限领数量
     */
    private Integer perUserLimit;
    
    /**
     * 有效期开始时间
     */
    private LocalDateTime validStartTime;
    
    /**
     * 有效期结束时间
     */
    private LocalDateTime validEndTime;
    
    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
    
    /**
     * 优惠券描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
    private String createBy;
    
    /**
     * 更新人
     */
    private String updateBy;
    
    /**
     * 检查模板是否有效
     */
    public boolean isValid() {
        LocalDateTime now = LocalDateTime.now();
        return status == 1 && 
               validStartTime.isBefore(now) && 
               validEndTime.isAfter(now);
    }
    
    /**
     * 检查是否还有库存
     */
    public boolean hasStock() {
        return remainingCount != null && remainingCount > 0;
    }
    
    /**
     * 计算优惠金额
     * 
     * @param originalAmount 原始金额(分)
     * @return 优惠金额(分)
     */
    public Integer calculateDiscountAmount(Integer originalAmount) {
        if (originalAmount == null || originalAmount <= 0) {
            return 0;
        }
        
        switch (templateType) {
            case 1: // 满减券
                if (minAmount != null && originalAmount < minAmount) {
                    return 0;
                }
                return discountAmount;
                
            case 2: // 折扣券
                int discountAmt = (int) (originalAmount * (1 - discountRate));
                if (maxDiscount != null && discountAmt > maxDiscount) {
                    return maxDiscount;
                }
                return discountAmt;
                
            case 3: // 立减券
                return Math.min(discountAmount, originalAmount);
                
            default:
                return 0;
        }
    }
}
