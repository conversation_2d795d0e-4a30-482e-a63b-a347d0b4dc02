package com.blsc.marketing.coupon.bean.bo;

import lombok.Data;

/**
 * 优惠券优惠详情业务对象
 */
@Data
public class CouponDiscountDetailBO {
    
    /**
     * 优惠券码
     */
    private String couponCode;
    
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 优惠券类型
     */
    private Integer templateType;
    
    /**
     * 优惠金额(分)
     */
    private Integer discountAmount;
    
    /**
     * 是否使用成功
     */
    private Boolean used = false;
    
    /**
     * 失败原因
     */
    private String failReason;
}
