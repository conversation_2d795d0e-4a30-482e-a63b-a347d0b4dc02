package com.blsc.marketing.coupon.bean.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户优惠券实体类
 * 对应数据库表：user_coupon
 */
@Data
public class UserCouponPO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 优惠券码，唯一标识
     */
    private String couponCode;
    
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 状态：1-未使用，2-已使用，3-已过期
     */
    private Integer status;
    
    /**
     * 使用时间
     */
    private LocalDateTime usedTime;
    
    /**
     * 使用的订单ID
     */
    private String usedOrderId;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
