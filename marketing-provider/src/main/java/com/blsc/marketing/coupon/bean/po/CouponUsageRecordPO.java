package com.blsc.marketing.coupon.bean.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 优惠券使用记录实体类
 * 对应数据库表：coupon_usage_record
 */
@Data
public class CouponUsageRecordPO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 优惠券码
     */
    private String couponCode;
    
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 订单ID
     */
    private String orderId;
    
    /**
     * 原始金额(分)
     */
    private Integer originalAmount;
    
    /**
     * 优惠金额(分)
     */
    private Integer discountAmount;
    
    /**
     * 最终金额(分)
     */
    private Integer finalAmount;
    
    /**
     * 使用时间
     */
    private LocalDateTime usedTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
