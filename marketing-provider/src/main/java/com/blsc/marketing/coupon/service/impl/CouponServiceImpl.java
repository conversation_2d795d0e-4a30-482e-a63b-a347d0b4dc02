package com.blsc.marketing.coupon.service.impl;

import com.blsc.marketing.coupon.bean.bo.PageBO;
import com.blsc.marketing.coupon.bean.bo.UserCouponBO;
import com.blsc.marketing.coupon.bean.bo.ValidateCouponBO;
import com.blsc.marketing.coupon.bean.bo.UseCouponBO;
import com.blsc.marketing.coupon.bean.dto.ClaimCouponDTO;
import com.blsc.marketing.coupon.bean.dto.QueryUserCouponsDTO;
import com.blsc.marketing.coupon.bean.dto.QueryUsageRecordsDTO;
import com.blsc.marketing.coupon.bean.dto.UseCouponDTO;
import com.blsc.marketing.coupon.bean.dto.ValidateCouponDTO;
import com.blsc.marketing.coupon.bean.po.CouponIssueRecordPO;
import com.blsc.marketing.coupon.bean.po.CouponUsageRecordPO;
import com.blsc.marketing.coupon.bean.po.UserCouponPO;
import com.blsc.marketing.coupon.bean.query.UserCouponsQuery;
import com.blsc.marketing.coupon.bean.query.UsageRecordsQuery;
import com.blsc.marketing.coupon.bean.CouponConstants;
import com.blsc.marketing.coupon.util.CouponExceptionHelper;
import com.blsc.marketing.coupon.mapper.CouponIssueRecordMapper;
import com.blsc.marketing.coupon.mapper.CouponTemplateMapper;
import com.blsc.marketing.coupon.mapper.UserCouponMapper;
import com.blsc.marketing.coupon.service.CouponCacheService;
import com.blsc.marketing.coupon.service.CouponService;
import com.blsc.marketing.coupon.service.CouponTemplateService;
import com.blsc.marketing.coupon.util.CouponCodeGenerator;
import com.blsc.marketing.coupon.util.DTOConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 优惠券服务实现 CouponServiceImpl -> CouponTemplateService/CouponCacheService 单向调用
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CouponServiceImpl implements CouponService {
    
    private final UserCouponMapper userCouponMapper;
    
    private final CouponTemplateMapper couponTemplateMapper;
    
    private final CouponIssueRecordMapper couponIssueRecordMapper;

    private final CouponTemplateService couponTemplateService;
    
    private final CouponCacheService couponCacheService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public String claimCoupon(ClaimCouponDTO dto) {
        log.info("用户领取优惠券，用户ID：{}，模板ID：{}", dto.getUserId(), dto.getTemplateId());
        
        try {
            // 获取分布式锁，防止并发领取
            String lockKey = "claim_coupon:" + dto.getUserId() + ":" + dto.getTemplateId();
            if (!couponCacheService.tryLock(lockKey, 30)) {
                throw CouponExceptionHelper.claimCouponFailedException("系统繁忙，请稍后重试");
            }
            
            try {
                // 检查模板是否可以发放
                if (!couponTemplateService.canIssueToUser(dto.getTemplateId(), dto.getUserId())) {
                    throw CouponExceptionHelper.claimCouponFailedException("优惠券不可领取");
                }
                
                // 生成优惠券码
                String couponCode = CouponCodeGenerator.generateCouponCode(dto.getTemplateId());
                
                // 创建用户优惠券
                UserCouponPO userCoupon = buildUserCouponPO(dto, couponCode);
                int result = userCouponMapper.insert(userCoupon);
                if (result <= 0) {
                    throw CouponExceptionHelper.claimCouponFailedException("创建用户优惠券失败");
                }
                
                // 创建发放记录
                CouponIssueRecordPO issueRecord = buildIssueRecordPO(dto, couponCode);
                couponIssueRecordMapper.insert(issueRecord);
                
                // 注意：不需要更新模板表中的发放数量，因为数据库表中没有这些字段
                
                // 更新缓存（只更新计数缓存）
                couponCacheService.incrementIssuedCount(dto.getTemplateId(), 1L);
                couponCacheService.incrementUserClaimedCount(dto.getUserId(), dto.getTemplateId(), 1L);
                
                log.info("用户领取优惠券成功，用户ID：{}，优惠券码：{}", dto.getUserId(), couponCode);
                return couponCode;
                
            } finally {
                couponCacheService.releaseLock(lockKey);
            }
            
        } catch (Exception e) {
            log.error("用户领取优惠券失败，用户ID：{}，模板ID：{}", dto.getUserId(), dto.getTemplateId(), e);
            throw CouponExceptionHelper.claimCouponFailedException("领取优惠券失败：" + e.getMessage());
        }
    }
    
    @Override
    public PageBO<UserCouponBO> queryUserCoupons(QueryUserCouponsDTO dto) {
        log.debug("查询用户优惠券，用户ID：{}", dto.getUserId());
        
        try {
            // 转换查询条件
            UserCouponsQuery query = DTOConverter.convertToQuery(dto);
            
            // 查询总数
            Integer total = userCouponMapper.countByCondition(query);
            if (total == 0) {
                return new PageBO<>(new ArrayList<>(), 0, dto.getPageNum(), dto.getPageSize());
            }
            
            // 查询数据
            List<UserCouponBO> userCoupons = userCouponMapper.selectByCondition(query);
            
            return new PageBO<>(userCoupons, total, dto.getPageNum(), dto.getPageSize());
            
        } catch (Exception e) {
            log.error("查询用户优惠券失败，用户ID：{}", dto.getUserId(), e);
            throw CouponExceptionHelper.queryCouponFailedException("查询用户优惠券失败：" + e.getMessage());
        }
    }
    
    @Override
    public UserCouponBO getCouponByCode(String couponCode) {
        if (!StringUtils.hasText(couponCode)) {
            throw CouponExceptionHelper.invalidParameterException("优惠券码不能为空");
        }
        
        log.debug("根据优惠券码查询优惠券详情，优惠券码：{}", couponCode);
        
        UserCouponBO userCoupon = userCouponMapper.selectDetailByCouponCode(couponCode);
        if (userCoupon == null) {
            throw CouponExceptionHelper.couponNotFoundException(couponCode);
        }
        
        return userCoupon;
    }
    
    @Override
    public List<UserCouponBO> getCouponsByCodes(List<String> couponCodes) {
        if (CollectionUtils.isEmpty(couponCodes)) {
            return new ArrayList<>();
        }
        
        log.debug("批量查询优惠券详情，优惠券码数量：{}", couponCodes.size());
        
        return userCouponMapper.selectDetailByCouponCodes(couponCodes);
    }
    
    @Override
    public ValidateCouponBO validateCoupon(ValidateCouponDTO dto) {
        log.debug("验证优惠券，优惠券码：{}，用户ID：{}", dto.getCouponCode(), dto.getUserId());
        
        try {
            ValidateCouponBO result = new ValidateCouponBO();
            result.setValid(false);
            
            // 查询优惠券
            UserCouponBO userCoupon = getCouponByCode(dto.getCouponCode());
            
            // 检查优惠券是否属于用户
            if (!userCoupon.getUserId().equals(dto.getUserId())) {
                result.setMessage("优惠券不属于当前用户");
                return result;
            }
            
            // 检查优惠券状态
            if (!userCoupon.getStatus().equals(CouponConstants.CouponStatus.UNUSED)) {
                result.setMessage("优惠券状态无效");
                return result;
            }
            
            // 检查有效期（使用expireTime字段）
            LocalDateTime now = LocalDateTime.now();
            if (userCoupon.getExpireTime().isBefore(now)) {
                result.setMessage("优惠券已过期");
                return result;
            }
            
            // 检查使用范围
            if (!validateCouponScope(userCoupon, dto.getAppId(), dto.getProductId())) {
                result.setMessage("优惠券使用范围不匹配");
                return result;
            }
            
            // 计算优惠金额
            Integer discountAmount = calculateDiscountAmount(userCoupon, dto.getOrderAmount());
            if (discountAmount <= 0) {
                result.setMessage("不满足优惠券使用条件");
                return result;
            }

            result.setValid(true);
            result.setMessage("优惠券验证通过");
            result.setDiscountAmount(discountAmount);
            result.setFinalAmount(dto.getOrderAmount() - discountAmount);
            
            return result;
            
        } catch (Exception e) {
            log.error("验证优惠券失败，优惠券码：{}", dto.getCouponCode(), e);
            throw CouponExceptionHelper.validateCouponFailedException("验证优惠券失败：" + e.getMessage());
        }
    }
    
    /**
     * 构建用户优惠券PO对象
     */
    private UserCouponPO buildUserCouponPO(ClaimCouponDTO dto, String couponCode) {
        UserCouponPO userCoupon = new UserCouponPO();
        userCoupon.setCouponCode(couponCode);
        userCoupon.setTemplateId(dto.getTemplateId());
        userCoupon.setUserId(dto.getUserId());
        userCoupon.setAppId(dto.getAppId());
        userCoupon.setStatus(CouponConstants.CouponStatus.UNUSED);
        userCoupon.setCreateTime(LocalDateTime.now());
        userCoupon.setUpdateTime(LocalDateTime.now());
        
        return userCoupon;
    }
    
    /**
     * 构建发放记录PO对象
     */
    private CouponIssueRecordPO buildIssueRecordPO(ClaimCouponDTO dto, String couponCode) {
        CouponIssueRecordPO issueRecord = new CouponIssueRecordPO();
        issueRecord.setCouponCode(couponCode);
        issueRecord.setTemplateId(dto.getTemplateId());
        issueRecord.setUserId(dto.getUserId());
        issueRecord.setAppId(dto.getAppId());
        issueRecord.setIssueType(CouponConstants.IssueType.USER_CLAIM);
        issueRecord.setIssueTime(LocalDateTime.now());
        
        return issueRecord;
    }
    
    // 删除updateTemplateIssuedCount方法，因为数据库表中没有issuedCount字段

    @Override
    @Transactional(rollbackFor = Exception.class)
    public UseCouponBO useCoupon(UseCouponDTO dto) {
        log.info("使用优惠券，优惠券码：{}，用户ID：{}，订单ID：{}",
                dto.getCouponCode(), dto.getUserId(), dto.getOrderId());

        try {
            // 获取分布式锁，防止重复使用
            String lockKey = "use_coupon:" + dto.getCouponCode();
            if (!couponCacheService.tryLock(lockKey, 30)) {
                throw CouponExceptionHelper.useCouponFailedException("系统繁忙，请稍后重试");
            }

            try {
                // 验证优惠券
                ValidateCouponDTO validateDTO = new ValidateCouponDTO();
                validateDTO.setCouponCode(dto.getCouponCode());
                validateDTO.setUserId(dto.getUserId());
                validateDTO.setAppId(dto.getAppId());
                validateDTO.setOrderAmount(dto.getOriginalAmount());
                validateDTO.setProductId(dto.getProductId());

                ValidateCouponBO validateResult = validateCoupon(validateDTO);
                if (!validateResult.getValid()) {
                    throw CouponExceptionHelper.useCouponFailedException("优惠券验证失败：" + validateResult.getMessage());
                }

                // 更新优惠券状态为已使用
                int result = userCouponMapper.updateStatus(
                        dto.getCouponCode(),
                        CouponConstants.CouponStatus.USED,
                        LocalDateTime.now(),
                        dto.getOrderId(),
                        LocalDateTime.now()
                );

                if (result <= 0) {
                    throw CouponExceptionHelper.useCouponFailedException("更新优惠券状态失败");
                }

                // 构建返回结果
                UseCouponBO result1 = new UseCouponBO();
                result1.setSuccess(true);
                result1.setCouponCode(dto.getCouponCode());
                result1.setDiscountAmount(validateResult.getDiscountAmount());
                result1.setFinalAmount(validateResult.getFinalAmount());
                result1.setUsedTime(LocalDateTime.now());

                log.info("使用优惠券成功，优惠券码：{}，优惠金额：{}分",
                        dto.getCouponCode(), validateResult.getDiscountAmount());

                return result1;

            } finally {
                couponCacheService.releaseLock(lockKey);
            }

        } catch (Exception e) {
            log.error("使用优惠券失败，优惠券码：{}", dto.getCouponCode(), e);
            throw CouponExceptionHelper.useCouponFailedException("使用优惠券失败：" + e.getMessage());
        }
    }

    @Override
    public List<UserCouponBO> getAvailableCoupons(String userId, String appId) {
        if (!StringUtils.hasText(userId)) {
            return new ArrayList<>();
        }

        log.debug("查询用户可用优惠券，用户ID：{}，应用ID：{}", userId, appId);

        // 直接从数据库查询（已移除用户优惠券缓存）
        List<UserCouponBO> availableCoupons = userCouponMapper.selectAvailableCoupons(userId, appId);

        return availableCoupons;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int expireCoupons() {
        log.info("开始批量过期优惠券");

        try {
            LocalDateTime now = LocalDateTime.now();
            List<UserCouponPO> expiredCoupons = userCouponMapper.selectExpiringSoon(now);

            if (CollectionUtils.isEmpty(expiredCoupons)) {
                log.info("没有需要过期的优惠券");
                return 0;
            }

            List<String> couponCodes = expiredCoupons.stream()
                    .map(UserCouponPO::getCouponCode)
                    .collect(Collectors.toList());

            int result = userCouponMapper.batchUpdateToExpired(couponCodes, now);

            log.info("批量过期优惠券完成，过期数量：{}", result);
            return result;

        } catch (Exception e) {
            log.error("批量过期优惠券失败", e);
            throw CouponExceptionHelper.expireCouponFailedException("批量过期优惠券失败：" + e.getMessage());
        }
    }

    @Override
    public boolean isCouponBelongsToUser(String couponCode, String userId) {
        if (!StringUtils.hasText(couponCode) || !StringUtils.hasText(userId)) {
            return false;
        }

        try {
            UserCouponPO userCoupon = userCouponMapper.selectByCouponCode(couponCode);
            return userCoupon != null && userId.equals(userCoupon.getUserId());
        } catch (Exception e) {
            log.error("检查优惠券归属失败，优惠券码：{}，用户ID：{}", couponCode, userId, e);
            return false;
        }
    }

    /**
     * 验证优惠券使用范围
     */
    private boolean validateCouponScope(UserCouponBO userCoupon, String appId, String productId) {
        Integer scopeType = userCoupon.getScopeType();
        String scopeValue = userCoupon.getScopeValue();

        switch (scopeType) {
            case 1: // 平台级
                // 平台级优惠券，所有应用都可以使用
                return true;

            case 2: // 应用级
                // 应用级优惠券，检查应用ID
                return appId != null && appId.equals(scopeValue);

            case 3: // 商品级
                // 商品级优惠券，检查商品ID
                if (productId == null || scopeValue == null) {
                    return false;
                }
                // scopeValue可能是JSON数组，这里简化处理
                return scopeValue.contains(productId);

            default:
                return false;
        }
    }

    /**
     * 计算优惠金额
     */
    private Integer calculateDiscountAmount(UserCouponBO userCoupon, Integer originalAmount) {
        if (originalAmount == null || originalAmount <= 0) {
            return 0;
        }

        Integer templateType = userCoupon.getTemplateType();

        switch (templateType) {
            case 1: // 满减券
                // 满减券
                Integer minAmount = userCoupon.getMinAmount();
                if (minAmount != null && originalAmount < minAmount) {
                    return 0;
                }
                return userCoupon.getDiscountAmount();

            case 2: // 折扣券
                // 折扣券
                Double discountRate = userCoupon.getDiscountRate();
                if (discountRate == null) {
                    return 0;
                }
                int discountAmt = (int) (originalAmount * (1 - discountRate));
                Integer maxDiscount = userCoupon.getMaxDiscount();
                if (maxDiscount != null && discountAmt > maxDiscount) {
                    return maxDiscount;
                }
                return discountAmt;

            case 3: // 立减券
                // 立减券
                return Math.min(userCoupon.getDiscountAmount(), originalAmount);

            default:
                return 0;
        }
    }

    /**
     * 构建使用记录PO对象
     */
    private CouponUsageRecordPO buildUsageRecordPO(UseCouponDTO dto, Integer discountAmount) {
        CouponUsageRecordPO usageRecord = new CouponUsageRecordPO();
        usageRecord.setCouponCode(dto.getCouponCode());
        usageRecord.setUserId(dto.getUserId());
        usageRecord.setAppId(dto.getAppId());
        usageRecord.setOrderId(dto.getOrderId());
        usageRecord.setOriginalAmount(dto.getOriginalAmount());
        usageRecord.setDiscountAmount(discountAmount);
        usageRecord.setFinalAmount(dto.getOriginalAmount() - discountAmount);
        // productId字段不在数据库表中
        usageRecord.setUsedTime(LocalDateTime.now());

        return usageRecord;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<String> batchIssueCoupons(Long templateId, List<String> userIds, String appId) {
        if (templateId == null || CollectionUtils.isEmpty(userIds)) {
            return new ArrayList<>();
        }

        log.info("批量发放优惠券，模板ID：{}，用户数量：{}", templateId, userIds.size());

        List<String> successCouponCodes = new ArrayList<>();

        for (String userId : userIds) {
            try {
                ClaimCouponDTO claimDTO = new ClaimCouponDTO();
                claimDTO.setTemplateId(templateId);
                claimDTO.setUserId(userId);
                claimDTO.setAppId(appId);

                String couponCode = claimCoupon(claimDTO);
                successCouponCodes.add(couponCode);

            } catch (Exception e) {
                log.warn("批量发放优惠券失败，用户ID：{}，模板ID：{}", userId, templateId, e);
                // 继续处理下一个用户，不中断整个批量操作
            }
        }

        log.info("批量发放优惠券完成，成功数量：{}/{}", successCouponCodes.size(), userIds.size());
        return successCouponCodes;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean revokeCoupon(String couponCode, String reason) {
        if (!StringUtils.hasText(couponCode)) {
            throw CouponExceptionHelper.invalidParameterException("优惠券码不能为空");
        }

        log.info("撤销优惠券，优惠券码：{}，原因：{}", couponCode, reason);

        try {
            // 查询优惠券
            UserCouponPO userCoupon = userCouponMapper.selectByCouponCode(couponCode);
            if (userCoupon == null) {
                throw CouponExceptionHelper.couponNotFoundException(couponCode);
            }

            // 检查优惠券状态，只能撤销未使用的优惠券
            if (!userCoupon.getStatus().equals(CouponConstants.CouponStatus.UNUSED)) {
                throw CouponExceptionHelper.revokeCouponFailedException("只能撤销未使用的优惠券");
            }

            // 更新优惠券状态为已撤销（这里使用过期状态代替撤销状态）
            int result = userCouponMapper.updateStatus(
                    couponCode,
                    CouponConstants.CouponStatus.EXPIRED,
                    null,
                    null,
                    LocalDateTime.now()
            );

            if (result <= 0) {
                throw CouponExceptionHelper.revokeCouponFailedException("更新优惠券状态失败");
            }

            // 注意：不需要更新模板表中的发放数量，因为数据库表中没有这些字段

            // 更新缓存（只更新计数缓存）
            couponCacheService.incrementIssuedCount(userCoupon.getTemplateId(), -1L);
            // 清除用户在该模板下的领取数量缓存，因为撤销后数量会减少
            couponCacheService.evictUserClaimedCount(userCoupon.getUserId(), userCoupon.getTemplateId());

            log.info("撤销优惠券成功，优惠券码：{}", couponCode);
            return true;

        } catch (Exception e) {
            log.error("撤销优惠券失败，优惠券码：{}", couponCode, e);
            throw CouponExceptionHelper.revokeCouponFailedException("撤销优惠券失败：" + e.getMessage());
        }
    }

    @Override
    public Integer getUserClaimedCount(String userId, Long templateId) {
        if (!StringUtils.hasText(userId) || templateId == null) {
            return 0;
        }

        log.debug("查询用户已领取优惠券数量，用户ID：{}，模板ID：{}", userId, templateId);

        // 先从缓存获取
        Long cachedCount = couponCacheService.getUserClaimedCount(userId, templateId);
        if (cachedCount != null) {
            return cachedCount.intValue();
        }

        // 从数据库查询
        Integer count = userCouponMapper.countUserClaimedCoupons(userId, templateId);
        if (count == null) {
            count = 0;
        }

        // 缓存结果
        couponCacheService.setUserClaimedCount(userId, templateId, count.longValue());

        return count;
    }

    @Override
    public Integer getTemplateIssuedCount(Long templateId) {
        if (templateId == null) {
            return 0;
        }

        log.debug("查询模板已发放优惠券数量，模板ID：{}", templateId);

        // 先从缓存获取
        Long cachedCount = couponCacheService.getIssuedCount(templateId);
        if (cachedCount != null) {
            return cachedCount.intValue();
        }

        // 从数据库查询
        Integer count = userCouponMapper.countIssuedCoupons(templateId);
        if (count == null) {
            count = 0;
        }

        // 缓存结果
        couponCacheService.setIssuedCount(templateId, count.longValue());

        return count;
    }

    @Override
    public Boolean preIssueCoupon(ClaimCouponDTO dto) {
        log.debug("预发放优惠券检查，用户ID：{}，模板ID：{}", dto.getUserId(), dto.getTemplateId());

        try {
            // 检查模板是否可以发放（不实际发放）
            return couponTemplateService.canIssueToUser(dto.getTemplateId(), dto.getUserId());

        } catch (Exception e) {
            log.error("预发放优惠券检查失败，用户ID：{}，模板ID：{}", dto.getUserId(), dto.getTemplateId(), e);
            return false;
        }
    }
}
