package com.blsc.marketing.coupon.service.impl;

import com.blsc.marketing.coupon.bean.bo.BatchCalculatePriceBO;
import com.blsc.marketing.coupon.bean.bo.CalculatePriceBO;
import com.blsc.marketing.coupon.bean.bo.CouponDiscountDetailBO;
import com.blsc.marketing.coupon.bean.bo.RecommendCouponsBO;
import com.blsc.marketing.coupon.bean.bo.RecommendedCouponBO;
import com.blsc.marketing.coupon.bean.bo.UserCouponBO;
import com.blsc.marketing.coupon.bean.dto.BatchCalculatePriceDTO;
import com.blsc.marketing.coupon.bean.dto.CalculatePriceDTO;
import com.blsc.marketing.coupon.bean.dto.RecommendCouponsDTO;
import com.blsc.marketing.coupon.util.CouponExceptionHelper;
import com.blsc.marketing.coupon.service.CouponService;
import com.blsc.marketing.coupon.service.MarketingPriceService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 营销价格计算服务实现
 */
@Slf4j
@Service
public class MarketingPriceServiceImpl implements MarketingPriceService {
    
    @Autowired
    private CouponService couponService;
    
    @Override
    public CalculatePriceBO calculatePrice(CalculatePriceDTO dto) {
        log.info("开始计算营销价格，用户ID：{}，应用ID：{}，原始金额：{}分",
                dto.getUserId(), dto.getAppId(), dto.getOriginalAmount());

        try {
            CalculatePriceBO response = new CalculatePriceBO();
            response.setOriginalAmount(dto.getOriginalAmount());
            response.setActivityDiscountAmount(0); // 暂时没有活动优惠

            // 如果没有优惠券，直接返回原价
            if (CollectionUtils.isEmpty(dto.getCouponCodes())) {
                response.setTotalDiscountAmount(0);
                response.setFinalAmount(dto.getOriginalAmount());
                response.setCouponDetails(new ArrayList<>());
                response.setSuccess(true);
                return response;
            }
            
            // 获取优惠券详情
            List<UserCouponBO> coupons = couponService.getCouponsByCodes(dto.getCouponCodes());

            // 计算优惠券优惠
            List<CouponDiscountDetailBO> couponDetails = new ArrayList<>();
            int totalCouponDiscount = 0;

            for (String couponCode : dto.getCouponCodes()) {
                CouponDiscountDetailBO detail = new CouponDiscountDetailBO();
                detail.setCouponCode(couponCode);
                
                // 查找对应的优惠券
                UserCouponBO coupon = coupons.stream()
                        .filter(c -> c.getCouponCode().equals(couponCode))
                        .findFirst()
                        .orElse(null);
                
                if (coupon == null) {
                    detail.setUsed(false);
                    detail.setFailReason("优惠券不存在");
                    couponDetails.add(detail);
                    continue;
                }
                
                detail.setTemplateId(coupon.getTemplateId());
                detail.setTemplateName(coupon.getTemplateName());
                detail.setTemplateType(coupon.getTemplateType());
                
                // 验证优惠券可用性
                String validationResult = validateCouponForUse(coupon, dto);
                if (validationResult != null) {
                    detail.setUsed(false);
                    detail.setFailReason(validationResult);
                    detail.setDiscountAmount(0);
                    couponDetails.add(detail);
                    continue;
                }
                
                // 计算优惠金额
                int discountAmount = coupon.calculateDiscountAmount(dto.getOriginalAmount());
                detail.setDiscountAmount(discountAmount);
                detail.setUsed(true);

                totalCouponDiscount += discountAmount;
                couponDetails.add(detail);

                // 如果不是预计算，实际使用优惠券
                if (!dto.getPreCalculate()) {
                    // TODO: 调用优惠券使用接口
                }
            }

            // 确保最终金额不为负数
            int finalAmount = Math.max(0, dto.getOriginalAmount() - totalCouponDiscount);
            
            response.setTotalDiscountAmount(totalCouponDiscount);
            response.setFinalAmount(finalAmount);
            response.setCouponDetails(couponDetails);
            response.setSuccess(true);
            response.setPreCalculate(dto.getPreCalculate() != null ? dto.getPreCalculate() : false);
            
            log.info("营销价格计算完成，原始金额：{}分，优惠金额：{}分，最终金额：{}分",
                    dto.getOriginalAmount(), totalCouponDiscount, finalAmount);

            return response;

        } catch (Exception e) {
            log.error("计算营销价格失败", e);
            CalculatePriceBO response = new CalculatePriceBO();
            response.setOriginalAmount(dto.getOriginalAmount());
            response.setTotalDiscountAmount(0);
            response.setFinalAmount(dto.getOriginalAmount());
            response.setSuccess(false);
            response.setMessage("计算营销价格失败：" + e.getMessage());
            response.setPreCalculate(dto.getPreCalculate() != null ? dto.getPreCalculate() : false);
            return response;
        }
    }
    
    @Override
    public RecommendCouponsBO recommendCoupons(RecommendCouponsDTO dto) {
        log.info("开始推荐优惠券，用户ID：{}，应用ID：{}，订单金额：{}分",
                dto.getUserId(), dto.getAppId(), dto.getOrderAmount());

        try {
            // 获取用户可用的优惠券
            List<UserCouponBO> availableCoupons = couponService.getAvailableCoupons(dto.getUserId(), dto.getAppId());

            // 过滤适用的优惠券并计算优惠金额
            List<RecommendedCouponBO> recommendedCoupons = availableCoupons.stream()
                    .filter(coupon -> isCouponApplicable(coupon, dto))
                    .map(coupon -> convertToRecommendedCoupon(coupon, dto.getOrderAmount()))
                    .sorted((c1, c2) -> c2.getPriority().compareTo(c1.getPriority())) // 按优先级降序排序
                    .limit(dto.getLimit())
                    .collect(Collectors.toList());
            
            // 计算最优组合
            RecommendCouponsBO response = new RecommendCouponsBO();
            response.setRecommendedCoupons(recommendedCoupons);

            if (!recommendedCoupons.isEmpty()) {
                // 简单策略：选择优惠金额最大的优惠券
                RecommendedCouponBO bestCoupon = recommendedCoupons.get(0);
                response.setBestCombinationDiscount(bestCoupon.getEstimatedDiscount());
                response.setBestCombinationCoupons(Arrays.asList(bestCoupon.getCouponCode()));
            } else {
                response.setBestCombinationDiscount(0);
                response.setBestCombinationCoupons(new ArrayList<>());
            }
            
            log.info("优惠券推荐完成，推荐数量：{}，最优优惠金额：{}分", 
                    recommendedCoupons.size(), response.getBestCombinationDiscount());
            
            return response;
            
        } catch (Exception e) {
            log.error("推荐优惠券失败", e);
            throw CouponExceptionHelper.internalError("推荐优惠券失败", e);
        }
    }
    
    @Override
    public BatchCalculatePriceBO batchCalculatePrice(BatchCalculatePriceDTO dto) {
        log.info("开始批量计算营销价格，请求数量：{}", dto.getRequests().size());

        List<CalculatePriceBO> responses = new ArrayList<>();
        int successCount = 0;
        int failCount = 0;

        for (CalculatePriceDTO calculateDto : dto.getRequests()) {
            try {
                CalculatePriceBO response = calculatePrice(calculateDto);
                responses.add(response);
                if (response.getSuccess()) {
                    successCount++;
                } else {
                    failCount++;
                }
            } catch (Exception e) {
                log.error("批量计算中单个请求失败", e);
                CalculatePriceBO errorResponse = new CalculatePriceBO();
                errorResponse.setSuccess(false);
                errorResponse.setMessage("计算失败：" + e.getMessage());
                responses.add(errorResponse);
                failCount++;
            }
        }

        BatchCalculatePriceBO response = new BatchCalculatePriceBO();
        response.setResponses(responses);
        response.setSuccessCount(successCount);
        response.setFailCount(failCount);

        log.info("批量计算营销价格完成，成功：{}，失败：{}", successCount, failCount);
        return response;
    }
    
    @Override
    public CalculatePriceBO preCalculatePrice(CalculatePriceDTO dto) {
        dto.setPreCalculate(true);
        return calculatePrice(dto);
    }
    
    @Override
    public RecommendCouponsBO getBestCouponCombination(RecommendCouponsDTO dto) {
        // 当前实现与推荐优惠券相同，未来可以实现更复杂的组合算法
        return recommendCoupons(dto);
    }
    
    /**
     * 验证优惠券是否可用于当前订单
     */
    private String validateCouponForUse(UserCouponBO coupon, CalculatePriceDTO dto) {
        // 检查优惠券是否属于当前用户
        if (!coupon.getUserId().equals(dto.getUserId())) {
            return "优惠券不属于当前用户";
        }
        
        // 检查优惠券状态
        if (!coupon.isAvailable()) {
            if (coupon.isUsed()) {
                return "优惠券已使用";
            } else if (coupon.isExpired()) {
                return "优惠券已过期";
            } else {
                return "优惠券状态异常";
            }
        }
        
        // 检查应用范围
        if (!coupon.isApplicableForApp(dto.getAppId())) {
            return "优惠券不适用于当前应用";
        }

        // 检查商品范围
        if (dto.getProductId() != null && !coupon.isApplicableForProduct(dto.getProductId())) {
            return "优惠券不适用于当前商品";
        }
        
        // 检查最低消费金额
        if (coupon.getTemplateType() == 1 && coupon.getMinAmount() != null) {
            if (dto.getOriginalAmount() < coupon.getMinAmount()) {
                return String.format("订单金额不满足使用条件，需满%d分", coupon.getMinAmount());
            }
        }
        
        return null; // 验证通过
    }
    
    /**
     * 检查优惠券是否适用于当前订单
     */
    private boolean isCouponApplicable(UserCouponBO coupon, RecommendCouponsDTO dto) {
        // 检查优惠券状态
        if (!coupon.isAvailable()) {
            return false;
        }

        // 检查应用范围
        if (!coupon.isApplicableForApp(dto.getAppId())) {
            return false;
        }

        // 检查商品范围
        if (dto.getProductId() != null && !coupon.isApplicableForProduct(dto.getProductId())) {
            return false;
        }

        // 检查最低消费金额
        if (coupon.getTemplateType() == 1 && coupon.getMinAmount() != null) {
            if (dto.getOrderAmount() < coupon.getMinAmount()) {
                return false;
            }
        }

        return true;
    }
    
    /**
     * 转换为推荐优惠券对象
     */
    private RecommendedCouponBO convertToRecommendedCoupon(UserCouponBO coupon, Integer orderAmount) {
        RecommendedCouponBO recommended = new RecommendedCouponBO();
        recommended.setCouponCode(coupon.getCouponCode());
        recommended.setTemplateName(coupon.getTemplateName());
        recommended.setTemplateType(coupon.getTemplateType());
        recommended.setEstimatedDiscount(coupon.calculateDiscountAmount(orderAmount));
        recommended.setExpireTime(coupon.getExpireTime().toString());

        // 生成使用条件描述
        String conditionDesc = generateConditionDesc(coupon);
        recommended.setConditionDesc(conditionDesc);

        // 计算优先级（优惠金额越大优先级越高）
        recommended.setPriority(recommended.getEstimatedDiscount());

        return recommended;
    }
    
    /**
     * 生成使用条件描述
     */
    private String generateConditionDesc(UserCouponBO coupon) {
        switch (coupon.getTemplateType()) {
            case 1: // 满减券
                if (coupon.getMinAmount() != null && coupon.getMinAmount() > 0) {
                    return String.format("满%d分减%d分", coupon.getMinAmount(), coupon.getDiscountAmount());
                } else {
                    return String.format("立减%d分", coupon.getDiscountAmount());
                }
            case 2: // 折扣券
                String desc = String.format("%.1f折", coupon.getDiscountRate() * 10);
                if (coupon.getMaxDiscount() != null && coupon.getMaxDiscount() > 0) {
                    desc += String.format("，最高优惠%d分", coupon.getMaxDiscount());
                }
                return desc;
            case 3: // 立减券
                return String.format("立减%d分", coupon.getDiscountAmount());
            default:
                return "优惠券";
        }
    }
}
