package com.blsc.marketing.coupon.service;

import com.blsc.marketing.coupon.bean.bo.CouponTemplateBO;
import com.blsc.marketing.coupon.bean.bo.PageBO;
import com.blsc.marketing.coupon.bean.dto.CreateCouponTemplateDTO;
import com.blsc.marketing.coupon.bean.dto.QueryCouponTemplatesDTO;

import java.util.List;

/**
 * 优惠券模板服务接口
 */
public interface CouponTemplateService {
    
    /**
     * 创建优惠券模板
     *
     * @param dto 创建DTO
     * @return 模板ID
     */
    Long createTemplate(CreateCouponTemplateDTO dto);
    
    /**
     * 根据ID查询优惠券模板
     * 
     * @param templateId 模板ID
     * @return 优惠券模板
     */
    CouponTemplateBO getTemplateById(Long templateId);
    
    /**
     * 根据ID列表查询优惠券模板
     * 
     * @param templateIds 模板ID列表
     * @return 优惠券模板列表
     */
    List<CouponTemplateBO> getTemplatesByIds(List<Long> templateIds);
    
    /**
     * 分页查询优惠券模板
     *
     * @param dto 查询DTO
     * @return 分页结果
     */
    PageBO<CouponTemplateBO> queryTemplates(QueryCouponTemplatesDTO dto);
    
    /**
     * 更新优惠券模板
     *
     * @param templateId 模板ID
     * @param dto 更新DTO
     * @return 是否成功
     */
    Boolean updateTemplate(Long templateId, CreateCouponTemplateDTO dto);
    
    /**
     * 启用优惠券模板
     * 
     * @param templateId 模板ID
     * @param updateBy 更新人
     * @return 是否成功
     */
    Boolean enableTemplate(Long templateId, String updateBy);
    
    /**
     * 禁用优惠券模板
     * 
     * @param templateId 模板ID
     * @param updateBy 更新人
     * @return 是否成功
     */
    Boolean disableTemplate(Long templateId, String updateBy);
    
    /**
     * 批量更新模板状态
     * 
     * @param templateIds 模板ID列表
     * @param status 状态
     * @param updateBy 更新人
     * @return 更新数量
     */
    Integer batchUpdateStatus(List<Long> templateIds, Integer status, String updateBy);
    
    /**
     * 删除优惠券模板
     * 
     * @param templateId 模板ID
     * @return 是否成功
     */
    Boolean deleteTemplate(Long templateId);
    
    /**
     * 查询有效的优惠券模板
     * 
     * @return 有效模板列表
     */
    List<CouponTemplateBO> getValidTemplates();
    
    /**
     * 根据使用范围查询模板
     * 
     * @param scopeType 范围类型
     * @param scopeValue 范围值
     * @return 模板列表
     */
    List<CouponTemplateBO> getTemplatesByScope(Integer scopeType, String scopeValue);
    
    /**
     * 检查模板是否可以发放
     * 
     * @param templateId 模板ID
     * @param userId 用户ID
     * @return 检查结果
     */
    Boolean canIssueToUser(Long templateId, String userId);
    
    /**
     * 获取模板剩余库存
     * 
     * @param templateId 模板ID
     * @return 剩余库存
     */
    Integer getRemainingStock(Long templateId);
    
    /**
     * 查询即将过期的模板
     * 
     * @param hours 小时数
     * @return 即将过期的模板列表
     */
    List<CouponTemplateBO> getExpiringSoonTemplates(Integer hours);
}
