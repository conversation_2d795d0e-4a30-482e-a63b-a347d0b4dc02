package com.blsc.marketing.coupon.service.impl;

import com.blsc.marketing.coupon.bean.bo.CouponTemplateBO;
import com.blsc.marketing.coupon.bean.bo.PageBO;
import com.blsc.marketing.coupon.bean.dto.CreateCouponTemplateDTO;
import com.blsc.marketing.coupon.bean.dto.QueryCouponTemplatesDTO;
import com.blsc.marketing.coupon.bean.po.CouponTemplatePO;
import com.blsc.marketing.coupon.bean.query.CouponTemplatesQuery;
import com.blsc.marketing.coupon.bean.CouponConstants;
import com.blsc.marketing.coupon.util.CouponExceptionHelper;
import com.blsc.marketing.coupon.mapper.CouponTemplateMapper;
import com.blsc.marketing.coupon.mapper.UserCouponMapper;
import com.blsc.marketing.coupon.service.CouponCacheService;
import com.blsc.marketing.coupon.service.CouponTemplateService;
import com.blsc.marketing.coupon.util.BeanConverter;
import com.blsc.marketing.coupon.util.DTOConverter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 优惠券模板服务实现
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class CouponTemplateServiceImpl implements CouponTemplateService {
    
    private final CouponTemplateMapper couponTemplateMapper;

    private final UserCouponMapper userCouponMapper;

    private final CouponCacheService couponCacheService;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long createTemplate(CreateCouponTemplateDTO dto) {
        log.info("开始创建优惠券模板，模板名称：{}", dto.getTemplateName());
        
        try {
            // 参数验证
            validateCreateTemplateDTO(dto);
            
            // 构建PO对象
            CouponTemplatePO template = buildTemplatePO(dto);
            
            // 插入数据库
            int result = couponTemplateMapper.insertOne(template);
            if (result <= 0) {
                throw CouponExceptionHelper.createTemplateFailedException("插入数据库失败");
            }
            
            log.info("优惠券模板创建成功，模板ID：{}", template.getId());
            return template.getId();
            
        } catch (Exception e) {
            log.error("创建优惠券模板失败，模板名称：{}", dto.getTemplateName(), e);
            throw CouponExceptionHelper.createTemplateFailedException("创建模板失败：" + e.getMessage());
        }
    }
    
    @Override
    public CouponTemplateBO getTemplateById(Long templateId) {
        if (templateId == null) {
            throw CouponExceptionHelper.invalidParameterException("模板ID不能为空");
        }

        log.debug("查询优惠券模板，模板ID：{}", templateId);

        // 直接从数据库查询（移除模板缓存，因为模板数据变化不频繁）
        CouponTemplatePO templatePO = couponTemplateMapper.selectById(templateId);
        if (templatePO == null) {
            throw CouponExceptionHelper.templateNotFoundException(templateId);
        }

        CouponTemplateBO templateBO = BeanConverter.convertToBO(templatePO);

        return templateBO;
    }
    
    @Override
    public List<CouponTemplateBO> getTemplatesByIds(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return new ArrayList<>();
        }
        
        log.debug("批量查询优惠券模板，模板ID数量：{}", templateIds.size());
        
        List<CouponTemplatePO> templatePOs = couponTemplateMapper.selectByIds(templateIds);
        return templatePOs.stream()
                .map(BeanConverter::convertToBO)
                .collect(Collectors.toList());
    }
    
    @Override
    public PageBO<CouponTemplateBO> queryTemplates(QueryCouponTemplatesDTO dto) {
        log.debug("分页查询优惠券模板，查询条件：{}", dto);
        
        try {
            // 转换查询条件
            CouponTemplatesQuery query = DTOConverter.convertToQuery(dto);
            
            // 查询总数
            Integer total = couponTemplateMapper.countByCondition(query);
            if (total == 0) {
                return new PageBO<>(new ArrayList<>(), 0, dto.getPageNum(), dto.getPageSize());
            }
            
            // 查询数据
            List<CouponTemplatePO> templatePOs = couponTemplateMapper.selectByCondition(query);
            List<CouponTemplateBO> templateBOs = templatePOs.stream()
                    .map(BeanConverter::convertToBO)
                    .collect(Collectors.toList());
            
            return new PageBO<>(templateBOs, total, dto.getPageNum(), dto.getPageSize());
            
        } catch (Exception e) {
            log.error("分页查询优惠券模板失败", e);
            throw CouponExceptionHelper.queryTemplateFailedException("查询模板失败：" + e.getMessage());
        }
    }
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean updateTemplate(Long templateId, CreateCouponTemplateDTO dto) {
        log.info("开始更新优惠券模板，模板ID：{}", templateId);
        
        try {
            // 检查模板是否存在
            CouponTemplatePO existingTemplate = couponTemplateMapper.selectById(templateId);
            if (existingTemplate == null) {
                throw CouponExceptionHelper.templateNotFoundException(templateId);
            }
            
            // 参数验证
            validateCreateTemplateDTO(dto);
            
            // 构建更新对象
            CouponTemplatePO updateTemplate = buildTemplatePO(dto);
            updateTemplate.setId(templateId);
            updateTemplate.setUpdateTime(LocalDateTime.now());
            
            // 更新数据库
            int result = couponTemplateMapper.updateById(updateTemplate);
            if (result <= 0) {
                throw CouponExceptionHelper.updateTemplateFailedException("更新数据库失败");
            }
            
            // 清除相关缓存（模板更新后清除发放数量缓存）
            couponCacheService.evictIssuedCount(templateId);
            
            log.info("优惠券模板更新成功，模板ID：{}", templateId);
            return true;
            
        } catch (Exception e) {
            log.error("更新优惠券模板失败，模板ID：{}", templateId, e);
            throw CouponExceptionHelper.updateTemplateFailedException("更新模板失败：" + e.getMessage());
        }
    }
    
    /**
     * 验证创建模板DTO
     */
    private void validateCreateTemplateDTO(CreateCouponTemplateDTO dto) {
        // 验证优惠券类型
        if (dto.getTemplateType() < CouponConstants.CouponType.FULL_REDUCTION || dto.getTemplateType() > CouponConstants.CouponType.DIRECT_REDUCTION) {
            throw CouponExceptionHelper.invalidParameterException("无效的优惠券类型：" + dto.getTemplateType());
        }

        // 验证使用范围类型
        if (dto.getScopeType() < CouponConstants.ScopeType.PLATFORM || dto.getScopeType() > CouponConstants.ScopeType.PRODUCT) {
            throw CouponExceptionHelper.invalidParameterException("无效的使用范围类型：" + dto.getScopeType());
        }
        
        // 验证时间范围
        if (dto.getValidStartTime().isAfter(dto.getValidEndTime())) {
            throw CouponExceptionHelper.invalidParameterException("有效期开始时间不能晚于结束时间");
        }
        
        // 根据优惠券类型验证参数
        validateTemplateTypeSpecificParams(dto);
    }
    
    /**
     * 验证特定类型的参数
     */
    private void validateTemplateTypeSpecificParams(CreateCouponTemplateDTO dto) {
        switch (dto.getTemplateType()) {
            case CouponConstants.CouponType.FULL_REDUCTION: // 满减券
                // 满减券：必须有优惠金额和最低消费金额
                if (dto.getDiscountAmount() == null || dto.getDiscountAmount() <= 0) {
                    throw CouponExceptionHelper.invalidParameterException("满减券优惠金额必须大于0");
                }
                if (dto.getMinAmount() == null || dto.getMinAmount() <= 0) {
                    throw CouponExceptionHelper.invalidParameterException("满减券最低消费金额必须大于0");
                }
                if (dto.getDiscountAmount() >= dto.getMinAmount()) {
                    throw CouponExceptionHelper.invalidParameterException("优惠金额不能大于等于最低消费金额");
                }
                break;

            case CouponConstants.CouponType.DISCOUNT: // 折扣券
                // 折扣券：必须有折扣率
                if (dto.getDiscountRate() == null || dto.getDiscountRate() <= 0 || dto.getDiscountRate() >= 1) {
                    throw CouponExceptionHelper.invalidParameterException("折扣率必须在0-1之间");
                }
                break;

            case CouponConstants.CouponType.DIRECT_REDUCTION: // 立减券
                // 立减券：必须有优惠金额
                if (dto.getDiscountAmount() == null || dto.getDiscountAmount() <= 0) {
                    throw CouponExceptionHelper.invalidParameterException("立减券优惠金额必须大于0");
                }
                break;
        }
    }
    
    /**
     * 构建模板PO对象
     */
    private CouponTemplatePO buildTemplatePO(CreateCouponTemplateDTO dto) {
        CouponTemplatePO template = new CouponTemplatePO();
        template.setTemplateName(dto.getTemplateName());
        template.setTemplateType(dto.getTemplateType());
        template.setScopeType(dto.getScopeType());
        template.setScopeValue(dto.getScopeValue());
        template.setDiscountAmount(dto.getDiscountAmount());
        template.setDiscountRate(dto.getDiscountRate());
        template.setMinAmount(dto.getMinAmount());
        template.setMaxDiscount(dto.getMaxDiscount());
        template.setTotalCount(dto.getTotalCount());
        // 注意：issuedCount和remainingCount不在数据库表中，通过查询计算
        template.setPerUserLimit(dto.getPerUserLimit());
        template.setValidStartTime(dto.getValidStartTime());
        template.setValidEndTime(dto.getValidEndTime());
        template.setStatus(CouponConstants.TemplateStatus.ENABLED);
        template.setDescription(dto.getDescription());
        template.setCreateTime(LocalDateTime.now());
        template.setUpdateTime(LocalDateTime.now());
        // 注意：createBy和updateBy字段不在数据库表中
        
        return template;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean enableTemplate(Long templateId, String updateBy) {
        log.info("启用优惠券模板，模板ID：{}", templateId);
        return updateTemplateStatus(templateId, CouponConstants.TemplateStatus.ENABLED, updateBy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean disableTemplate(Long templateId, String updateBy) {
        log.info("禁用优惠券模板，模板ID：{}", templateId);
        return updateTemplateStatus(templateId, CouponConstants.TemplateStatus.DISABLED, updateBy);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer batchUpdateStatus(List<Long> templateIds, Integer status, String updateBy) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return 0;
        }

        log.info("批量更新模板状态，模板数量：{}，状态：{}", templateIds.size(), status);

        try {
            int result = couponTemplateMapper.batchUpdateStatus(templateIds, status, LocalDateTime.now(), updateBy);

            // 清除相关缓存（批量更新状态后清除发放数量缓存）
            templateIds.forEach(couponCacheService::evictIssuedCount);

            log.info("批量更新模板状态成功，更新数量：{}", result);
            return result;

        } catch (Exception e) {
            log.error("批量更新模板状态失败", e);
            throw CouponExceptionHelper.updateTemplateFailedException("批量更新状态失败：" + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteTemplate(Long templateId) {
        log.info("删除优惠券模板，模板ID：{}", templateId);

        try {
            // 检查模板是否存在
            CouponTemplatePO template = couponTemplateMapper.selectById(templateId);
            if (template == null) {
                throw CouponExceptionHelper.templateNotFoundException(templateId);
            }

            // 检查是否已有优惠券发放（通过查询user_coupon表）
            Integer issuedCount = userCouponMapper.countIssuedCoupons(templateId);
            if (issuedCount != null && issuedCount > 0) {
                throw CouponExceptionHelper.templateCannotDeleteException("模板已有优惠券发放，不能删除");
            }

            int result = couponTemplateMapper.deleteById(templateId);
            if (result <= 0) {
                throw CouponExceptionHelper.deleteTemplateFailedException("删除数据库记录失败");
            }

            // 清除相关缓存（删除模板后清除发放数量缓存）
            couponCacheService.evictIssuedCount(templateId);

            log.info("优惠券模板删除成功，模板ID：{}", templateId);
            return true;

        } catch (Exception e) {
            log.error("删除优惠券模板失败，模板ID：{}", templateId, e);
            throw CouponExceptionHelper.deleteTemplateFailedException("删除模板失败：" + e.getMessage());
        }
    }

    @Override
    public List<CouponTemplateBO> getValidTemplates() {
        log.debug("查询有效的优惠券模板");

        List<CouponTemplatePO> templatePOs = couponTemplateMapper.selectValidTemplates();
        return templatePOs.stream()
                .map(BeanConverter::convertToBO)
                .collect(Collectors.toList());
    }

    @Override
    public List<CouponTemplateBO> getTemplatesByScope(Integer scopeType, String scopeValue) {
        log.debug("根据使用范围查询模板，范围类型：{}，范围值：{}", scopeType, scopeValue);

        List<CouponTemplatePO> templatePOs = couponTemplateMapper.selectByScopeTypeAndValue(scopeType, scopeValue);
        return templatePOs.stream()
                .map(BeanConverter::convertToBO)
                .collect(Collectors.toList());
    }

    @Override
    public Boolean canIssueToUser(Long templateId, String userId) {
        if (templateId == null || !StringUtils.hasText(userId)) {
            return false;
        }

        log.debug("检查模板是否可以发放给用户，模板ID：{}，用户ID：{}", templateId, userId);

        try {
            // 获取模板信息
            CouponTemplateBO template = getTemplateById(templateId);

            // 检查模板是否有效
            if (!template.isValid()) {
                log.debug("模板无效，不能发放，模板ID：{}", templateId);
                return false;
            }

            // 检查库存（通过查询已发放数量）
            Integer issuedCount = getTemplateIssuedCount(templateId);
            if (issuedCount >= template.getTotalCount()) {
                log.debug("模板库存不足，不能发放，模板ID：{}", templateId);
                return false;
            }

            // 检查用户领取限制 - 修复缓存不存在时的处理逻辑
            Integer userClaimedCount = getUserClaimedCountWithFallback(userId, templateId);
            if (userClaimedCount >= template.getPerUserLimit()) {
                log.debug("用户已达到领取限制，不能发放，用户ID：{}，模板ID：{}，已领取：{}，限制：{}",
                        userId, templateId, userClaimedCount, template.getPerUserLimit());
                return false;
            }

            return true;

        } catch (Exception e) {
            log.error("检查模板发放条件失败，模板ID：{}，用户ID：{}", templateId, userId, e);
            return false;
        }
    }

    /**
     * 获取用户已领取数量，支持缓存失效时从数据库查询
     */
    private Integer getUserClaimedCountWithFallback(String userId, Long templateId) {
        // 先从缓存获取
        Long cachedCount = couponCacheService.getUserClaimedCount(userId, templateId);
        if (cachedCount != null) {
            log.debug("从缓存获取用户领取数量，用户ID：{}，模板ID：{}，数量：{}", userId, templateId, cachedCount);
            return cachedCount.intValue();
        }

        // 缓存不存在时从数据库查询
        log.debug("缓存不存在，从数据库查询用户领取数量，用户ID：{}，模板ID：{}", userId, templateId);
        Integer count = userCouponMapper.countUserClaimedCoupons(userId, templateId);
        if (count == null) {
            count = 0;
        }

        // 缓存查询结果
        couponCacheService.setUserClaimedCount(userId, templateId, count.longValue());

        return count;
    }

    @Override
    public Integer getRemainingStock(Long templateId) {
        if (templateId == null) {
            return 0;
        }

        log.debug("获取模板剩余库存，模板ID：{}", templateId);

        try {
            CouponTemplateBO template = getTemplateById(templateId);
            Integer issuedCount = getTemplateIssuedCount(templateId);
            return template.getTotalCount() - issuedCount;
        } catch (Exception e) {
            log.error("获取模板剩余库存失败，模板ID：{}", templateId, e);
            return 0;
        }
    }

    @Override
    public List<CouponTemplateBO> getExpiringSoonTemplates(Integer hours) {
        if (hours == null || hours <= 0) {
            hours = 24; // 默认24小时
        }

        log.debug("查询即将过期的模板，小时数：{}", hours);

        LocalDateTime expireTime = LocalDateTime.now().plusHours(hours);
        List<CouponTemplatePO> templatePOs = couponTemplateMapper.selectExpiringSoon(expireTime);

        return templatePOs.stream()
                .map(BeanConverter::convertToBO)
                .collect(Collectors.toList());
    }

    /**
     * 更新模板状态
     */
    private Boolean updateTemplateStatus(Long templateId, Integer status, String updateBy) {
        try {
            // 检查模板是否存在
            CouponTemplatePO template = couponTemplateMapper.selectById(templateId);
            if (template == null) {
                throw CouponExceptionHelper.templateNotFoundException(templateId);
            }

            // 构建更新对象
            CouponTemplatePO updateTemplate = new CouponTemplatePO();
            updateTemplate.setId(templateId);
            updateTemplate.setStatus(status);
            updateTemplate.setUpdateTime(LocalDateTime.now());
            // updateBy字段不在数据库表中

            int result = couponTemplateMapper.updateById(updateTemplate);
            if (result <= 0) {
                throw CouponExceptionHelper.updateTemplateFailedException("更新状态失败");
            }

            // 清除相关缓存（状态更新后清除发放数量缓存）
            couponCacheService.evictIssuedCount(templateId);

            return true;

        } catch (Exception e) {
            log.error("更新模板状态失败，模板ID：{}，状态：{}", templateId, status, e);
            throw CouponExceptionHelper.updateTemplateFailedException("更新状态失败：" + e.getMessage());
        }
    }

    /**
     * 获取模板已发放数量
     */
    private Integer getTemplateIssuedCount(Long templateId) {
        // 先从缓存获取
        Long cachedCount = couponCacheService.getIssuedCount(templateId);
        if (cachedCount != null) {
            return cachedCount.intValue();
        }

        // 从数据库查询
        Integer count = userCouponMapper.countIssuedCoupons(templateId);
        if (count == null) {
            count = 0;
        }

        // 缓存结果
        couponCacheService.setIssuedCount(templateId, count.longValue());

        return count;
    }
}
