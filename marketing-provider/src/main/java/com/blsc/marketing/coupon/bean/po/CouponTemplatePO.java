package com.blsc.marketing.coupon.bean.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 优惠券模板实体类
 * 对应数据库表：coupon_template
 */
@Data
public class CouponTemplatePO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 优惠券类型：1-满减券，2-折扣券，3-立减券
     */
    private Integer templateType;
    
    /**
     * 使用范围类型：1-平台级，2-应用级，3-商品级
     */
    private Integer scopeType;
    
    /**
     * 范围值：应用级存应用ID，商品级存商品ID列表(JSON)
     */
    private String scopeValue;
    
    /**
     * 优惠金额(分)，满减券和立减券使用
     */
    private Integer discountAmount;
    
    /**
     * 折扣率(0.01-0.99)，折扣券使用
     */
    private Double discountRate;
    
    /**
     * 最低消费金额(分)，满减券使用
     */
    private Integer minAmount;
    
    /**
     * 最大优惠金额(分)，折扣券使用
     */
    private Integer maxDiscount;
    
    /**
     * 发放总数量
     */
    private Integer totalCount;

    /**
     * 每用户限领数量
     */
    private Integer perUserLimit;
    
    /**
     * 有效期开始时间
     */
    private LocalDateTime validStartTime;
    
    /**
     * 有效期结束时间
     */
    private LocalDateTime validEndTime;
    
    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
    
    /**
     * 优惠券描述
     */
    private String description;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 创建人
     */
//    private String createBy;

    /**
     * 更新人
     */
//    private String updateBy;
}
