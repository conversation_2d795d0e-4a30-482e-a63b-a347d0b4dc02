package com.blsc.marketing.coupon.bean.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 优惠券发放记录实体类
 * 对应数据库表：coupon_issue_record
 */
@Data
public class CouponIssueRecordPO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 优惠券码
     */
    private String couponCode;
    
    /**
     * 发放类型：1-用户主动领取，2-系统定时发放，3-活动发放
     */
    private Integer issueType;
    
    /**
     * 发放时间
     */
    private LocalDateTime issueTime;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
