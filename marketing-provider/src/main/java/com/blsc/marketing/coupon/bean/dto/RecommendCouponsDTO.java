package com.blsc.marketing.coupon.bean.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

/**
 * 推荐优惠券DTO
 */
@Data
public class RecommendCouponsDTO {
    
    @NotBlank(message = "用户ID不能为空")
    private String userId;
    
    @NotBlank(message = "应用ID不能为空")
    private String appId;
    
    @NotNull(message = "订单金额不能为空")
    @Positive(message = "订单金额必须大于0")
    private Integer orderAmount;
    
    private String productId;
    
    /**
     * 推荐数量限制
     */
    private Integer limit = 5;
}
