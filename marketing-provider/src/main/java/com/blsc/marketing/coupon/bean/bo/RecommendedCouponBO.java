package com.blsc.marketing.coupon.bean.bo;

import lombok.Data;

/**
 * 推荐优惠券详情业务对象
 */
@Data
public class RecommendedCouponBO {
    
    /**
     * 优惠券码
     */
    private String couponCode;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 优惠券类型
     */
    private Integer templateType;
    
    /**
     * 预计优惠金额(分)
     */
    private Integer estimatedDiscount;
    
    /**
     * 使用条件描述
     */
    private String conditionDesc;
    
    /**
     * 过期时间
     */
    private String expireTime;
    
    /**
     * 推荐优先级（数值越大优先级越高）
     */
    private Integer priority;
}
