package com.blsc.marketing.coupon.service.impl;

import com.blsc.marketing.coupon.bean.bo.CouponTemplateBO;
import com.blsc.marketing.coupon.bean.bo.UserCouponBO;
import com.blsc.marketing.coupon.service.CouponCacheService;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RAtomicLong;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.Duration;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 优惠券缓存服务实现
 *
 * 优化说明：
 * 1. 移除模板和用户优惠券的缓存逻辑，只保留计数缓存
 * 2. 简化缓存键设计，提高性能
 * 3. 优化缓存过期时间设置
 */
@Slf4j
@Service
public class CouponCacheServiceImpl implements CouponCacheService {

    @Autowired
    private RedissonClient redissonClient;

    // 缓存键前缀 - 只保留必要的计数和锁相关缓存
    private static final String ISSUED_COUNT_PREFIX = "coupon:issued:";
    private static final String USER_CLAIMED_COUNT_PREFIX = "coupon:user_claimed:";
    private static final String LOCK_PREFIX = "coupon:lock:";

    // 缓存过期时间
    private static final Duration COUNT_CACHE_EXPIRE = Duration.ofHours(1);
    private static final Duration LOCK_EXPIRE = Duration.ofMinutes(5);

    // ===== 模板发放数量缓存相关方法 =====

    
    @Override
    public Long getIssuedCount(Long templateId) {
        if (templateId == null) {
            return null;
        }
        
        try {
            String key = ISSUED_COUNT_PREFIX + templateId;
            RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
            
            if (!atomicLong.isExists()) {
                return null;
            }
            
            long count = atomicLong.get();
            log.debug("从缓存获取模板发放数量，模板ID：{}，数量：{}", templateId, count);
            
            return count;
            
        } catch (Exception e) {
            log.error("从缓存获取模板发放数量失败，模板ID：{}", templateId, e);
            return null;
        }
    }
    
    @Override
    public void setIssuedCount(Long templateId, Long count) {
        if (templateId == null || count == null) {
            return;
        }
        
        try {
            String key = ISSUED_COUNT_PREFIX + templateId;
            RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
            
            atomicLong.set(count);
            atomicLong.expire(COUNT_CACHE_EXPIRE);
            
            log.debug("设置模板发放数量缓存，模板ID：{}，数量：{}", templateId, count);
            
        } catch (Exception e) {
            log.error("设置模板发放数量缓存失败，模板ID：{}", templateId, e);
        }
    }
    
    @Override
    public Long incrementIssuedCount(Long templateId, Long increment) {
        if (templateId == null || increment == null) {
            return null;
        }
        
        try {
            String key = ISSUED_COUNT_PREFIX + templateId;
            RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
            
            long newCount = atomicLong.addAndGet(increment);
            atomicLong.expire(COUNT_CACHE_EXPIRE);
            
            log.debug("增加模板发放数量缓存，模板ID：{}，增量：{}，新值：{}", templateId, increment, newCount);
            
            return newCount;
            
        } catch (Exception e) {
            log.error("增加模板发放数量缓存失败，模板ID：{}", templateId, e);
            return null;
        }
    }
    
    @Override
    public Long getUserClaimedCount(String userId, Long templateId) {
        if (!StringUtils.hasText(userId) || templateId == null) {
            return null;
        }
        
        try {
            String key = USER_CLAIMED_COUNT_PREFIX + userId + ":" + templateId;
            RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
            
            if (!atomicLong.isExists()) {
                return null;
            }

            long count = atomicLong.get();
            log.debug("从缓存获取用户领取数量，用户ID：{}，模板ID：{}，数量：{}", userId, templateId, count);

            return count;
            
        } catch (Exception e) {
            log.error("从缓存获取用户领取数量失败，用户ID：{}，模板ID：{}", userId, templateId, e);
            return null;
        }
    }
    
    @Override
    public Long incrementUserClaimedCount(String userId, Long templateId, Long increment) {
        if (!StringUtils.hasText(userId) || templateId == null || increment == null) {
            return null;
        }
        
        try {
            String key = USER_CLAIMED_COUNT_PREFIX + userId + ":" + templateId;
            RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
            
            long newCount = atomicLong.addAndGet(increment);
            atomicLong.expire(COUNT_CACHE_EXPIRE);
            
            log.debug("增加用户领取数量缓存，用户ID：{}，模板ID：{}，增量：{}，新值：{}", 
                    userId, templateId, increment, newCount);
            
            return newCount;
            
        } catch (Exception e) {
            log.error("增加用户领取数量缓存失败，用户ID：{}，模板ID：{}", userId, templateId, e);
            return null;
        }
    }
    
    @Override
    public void setUserClaimedCount(String userId, Long templateId, Long count) {
        if (!StringUtils.hasText(userId) || templateId == null || count == null) {
            return;
        }
        
        try {
            String key = USER_CLAIMED_COUNT_PREFIX + userId + ":" + templateId;
            RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
            
            atomicLong.set(count);
            atomicLong.expire(COUNT_CACHE_EXPIRE);
            
            log.debug("设置用户领取数量缓存，用户ID：{}，模板ID：{}，数量：{}", userId, templateId, count);
            
        } catch (Exception e) {
            log.error("设置用户领取数量缓存失败，用户ID：{}，模板ID：{}", userId, templateId, e);
        }
    }

    @Override
    public void evictUserClaimedCount(String userId, Long templateId) {
        if (!StringUtils.hasText(userId) || templateId == null) {
            return;
        }

        try {
            String key = USER_CLAIMED_COUNT_PREFIX + userId + ":" + templateId;
            RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
            atomicLong.delete();

            log.debug("清除用户领取数量缓存，用户ID：{}，模板ID：{}", userId, templateId);

        } catch (Exception e) {
            log.error("清除用户领取数量缓存失败，用户ID：{}，模板ID：{}", userId, templateId, e);
        }
    }

    @Override
    public void evictIssuedCount(Long templateId) {
        if (templateId == null) {
            return;
        }

        try {
            String key = ISSUED_COUNT_PREFIX + templateId;
            RAtomicLong atomicLong = redissonClient.getAtomicLong(key);
            atomicLong.delete();

            log.debug("清除模板发放数量缓存，模板ID：{}", templateId);

        } catch (Exception e) {
            log.error("清除模板发放数量缓存失败，模板ID：{}", templateId, e);
        }
    }

    // ===== 分布式锁相关方法 =====

    @Override
    public Boolean tryLock(String lockKey, Integer expireSeconds) {
        if (!StringUtils.hasText(lockKey) || expireSeconds == null || expireSeconds <= 0) {
            return false;
        }

        try {
            String key = LOCK_PREFIX + lockKey;
            RLock lock = redissonClient.getLock(key);

            boolean acquired = lock.tryLock(0, expireSeconds, TimeUnit.SECONDS);

            if (acquired) {
                log.debug("获取分布式锁成功，锁键：{}，过期时间：{}秒", lockKey, expireSeconds);
            } else {
                log.debug("获取分布式锁失败，锁键：{}", lockKey);
            }

            return acquired;

        } catch (Exception e) {
            log.error("获取分布式锁异常，锁键：{}", lockKey, e);
            return false;
        }
    }

    @Override
    public void releaseLock(String lockKey) {
        if (!StringUtils.hasText(lockKey)) {
            return;
        }

        try {
            String key = LOCK_PREFIX + lockKey;
            RLock lock = redissonClient.getLock(key);

            if (lock.isHeldByCurrentThread()) {
                lock.unlock();
                log.debug("释放分布式锁成功，锁键：{}", lockKey);
            } else {
                log.debug("锁不是由当前线程持有，无需释放，锁键：{}", lockKey);
            }

        } catch (Exception e) {
            log.error("释放分布式锁异常，锁键：{}", lockKey, e);
        }
    }

    @Override
    public void warmUpCache(List<Long> templateIds) {
        if (CollectionUtils.isEmpty(templateIds)) {
            return;
        }

        log.info("开始预热缓存，模板数量：{}", templateIds.size());

        // 这里可以实现缓存预热逻辑
        // 例如：预加载热门模板、预计算发放数量等
        for (Long templateId : templateIds) {
            try {
                // 预热模板缓存（这里需要调用模板服务获取数据）
                // 由于避免循环依赖，这里只是示例
                log.debug("预热模板缓存，模板ID：{}", templateId);

            } catch (Exception e) {
                log.error("预热模板缓存失败，模板ID：{}", templateId, e);
            }
        }

        log.info("缓存预热完成");
    }

    @Override
    public void clearAllCache() {
        log.info("开始清空所有缓存");

        try {
            // 只清空计数器缓存
            redissonClient.getKeys().deleteByPattern(ISSUED_COUNT_PREFIX + "*");
            redissonClient.getKeys().deleteByPattern(USER_CLAIMED_COUNT_PREFIX + "*");

            log.info("清空所有缓存完成");

        } catch (Exception e) {
            log.error("清空所有缓存失败", e);
        }
    }

    @Override
    public Boolean exists(String key) {
        if (!StringUtils.hasText(key)) {
            return false;
        }

        try {
            return redissonClient.getBucket(key).isExists();

        } catch (Exception e) {
            log.error("检查缓存是否存在失败，键：{}", key, e);
            return false;
        }
    }

    @Override
    public void expire(String key, Integer seconds) {
        if (!StringUtils.hasText(key) || seconds == null || seconds <= 0) {
            return;
        }

        try {
            RBucket<Object> bucket = redissonClient.getBucket(key);
            if (bucket.isExists()) {
                bucket.expire(Duration.ofSeconds(seconds));
                log.debug("设置缓存过期时间成功，键：{}，过期时间：{}秒", key, seconds);
            }

        } catch (Exception e) {
            log.error("设置缓存过期时间失败，键：{}", key, e);
        }
    }
}
