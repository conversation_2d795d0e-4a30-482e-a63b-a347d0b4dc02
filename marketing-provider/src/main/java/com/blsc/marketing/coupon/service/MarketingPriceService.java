package com.blsc.marketing.coupon.service;

import com.blsc.marketing.coupon.bean.bo.BatchCalculatePriceBO;
import com.blsc.marketing.coupon.bean.bo.CalculatePriceBO;
import com.blsc.marketing.coupon.bean.bo.RecommendCouponsBO;
import com.blsc.marketing.coupon.bean.dto.BatchCalculatePriceDTO;
import com.blsc.marketing.coupon.bean.dto.CalculatePriceDTO;
import com.blsc.marketing.coupon.bean.dto.RecommendCouponsDTO;

/**
 * 营销价格计算服务接口
 */
public interface MarketingPriceService {
    
    /**
     * 计算营销价格
     *
     * @param dto 计算DTO
     * @return 计算结果
     */
    CalculatePriceBO calculatePrice(CalculatePriceDTO dto);

    /**
     * 推荐优惠券
     *
     * @param dto 推荐DTO
     * @return 推荐结果
     */
    RecommendCouponsBO recommendCoupons(RecommendCouponsDTO dto);

    /**
     * 批量计算营销价格
     *
     * @param dto 批量计算DTO
     * @return 批量计算结果
     */
    BatchCalculatePriceBO batchCalculatePrice(BatchCalculatePriceDTO dto);
    
    /**
     * 预计算营销价格（不实际使用优惠券）
     *
     * @param dto 计算DTO
     * @return 计算结果
     */
    CalculatePriceBO preCalculatePrice(CalculatePriceDTO dto);

    /**
     * 获取最优优惠券组合
     *
     * @param dto 推荐DTO
     * @return 最优组合的优惠券码列表
     */
    RecommendCouponsBO getBestCouponCombination(RecommendCouponsDTO dto);
}
