package com.blsc.marketing.coupon.bean.bo;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户优惠券业务对象
 */
@Data
public class UserCouponBO {
    
    /**
     * ID
     */
    private Long id;
    
    /**
     * 优惠券码
     */
    private String couponCode;
    
    /**
     * 模板ID
     */
    private Long templateId;
    
    /**
     * 模板名称
     */
    private String templateName;
    
    /**
     * 优惠券类型
     */
    private Integer templateType;
    
    /**
     * 使用范围类型
     */
    private Integer scopeType;
    
    /**
     * 范围值
     */
    private String scopeValue;
    
    /**
     * 优惠金额(分)
     */
    private Integer discountAmount;
    
    /**
     * 折扣率
     */
    private Double discountRate;
    
    /**
     * 最低消费金额(分)
     */
    private Integer minAmount;
    
    /**
     * 最大优惠金额(分)
     */
    private Integer maxDiscount;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 状态：1-未使用，2-已使用，3-已过期
     */
    private Integer status;
    
    /**
     * 使用时间
     */
    private LocalDateTime usedTime;
    
    /**
     * 使用的订单ID
     */
    private String usedOrderId;
    
    /**
     * 过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
    
    /**
     * 优惠券描述
     */
    private String description;
    
    /**
     * 检查优惠券是否可用
     */
    public boolean isAvailable() {
        return status == 1 && expireTime.isAfter(LocalDateTime.now());
    }
    
    /**
     * 检查优惠券是否已使用
     */
    public boolean isUsed() {
        return status == 2;
    }
    
    /**
     * 检查优惠券是否已过期
     */
    public boolean isExpired() {
        return status == 3 || expireTime.isBefore(LocalDateTime.now());
    }
    
    /**
     * 检查是否适用于指定应用
     */
    public boolean isApplicableForApp(String targetAppId) {
        if (scopeType == 1) { // 平台级
            return true;
        } else if (scopeType == 2) { // 应用级
            return targetAppId.equals(scopeValue);
        }
        return false;
    }
    
    /**
     * 检查是否适用于指定商品
     */
    public boolean isApplicableForProduct(String productId) {
        if (scopeType == 1 || scopeType == 2) { // 平台级或应用级
            return true;
        } else if (scopeType == 3) { // 商品级
            return scopeValue != null && scopeValue.contains(productId);
        }
        return false;
    }
    
    /**
     * 计算优惠金额
     * 
     * @param originalAmount 原始金额(分)
     * @return 优惠金额(分)
     */
    public Integer calculateDiscountAmount(Integer originalAmount) {
        if (originalAmount == null || originalAmount <= 0 || !isAvailable()) {
            return 0;
        }
        
        switch (templateType) {
            case 1: // 满减券
                if (minAmount != null && originalAmount < minAmount) {
                    return 0;
                }
                return discountAmount;
                
            case 2: // 折扣券
                int discountAmt = (int) (originalAmount * (1 - discountRate));
                if (maxDiscount != null && discountAmt > maxDiscount) {
                    return maxDiscount;
                }
                return discountAmt;
                
            case 3: // 立减券
                return Math.min(discountAmount, originalAmount);
                
            default:
                return 0;
        }
    }
}
