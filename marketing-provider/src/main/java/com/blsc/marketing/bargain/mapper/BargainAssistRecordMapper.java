package com.blsc.marketing.bargain.mapper;

import com.blsc.marketing.bargain.bean.po.BargainAssistRecordPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 助力记录Mapper
 */
@Mapper
public interface BargainAssistRecordMapper {
    
    /**
     * 根据ID查询
     */
    BargainAssistRecordPO selectById(@Param("id") Long id);
    
    /**
     * 根据用户助力活动ID查询助力记录列表
     */
    List<BargainAssistRecordPO> selectByUserBargainId(@Param("userBargainId") Long userBargainId);
    
    /**
     * 根据用户助力活动ID和助力用户ID查询
     */
    BargainAssistRecordPO selectByUserBargainAndAssistUser(@Param("userBargainId") Long userBargainId, 
                                                           @Param("assistUserId") String assistUserId);
    
    /**
     * 查询用户的助力记录
     */
    List<BargainAssistRecordPO> selectByAssistUser(@Param("assistUserId") String assistUserId, 
                                                   @Param("appId") String appId,
                                                   @Param("offset") Integer offset, 
                                                   @Param("pageSize") Integer pageSize);
    
    /**
     * 统计用户的助力记录数量
     */
    int countByAssistUser(@Param("assistUserId") String assistUserId, @Param("appId") String appId);
    
    /**
     * 插入一条记录
     */
    int insertOne(BargainAssistRecordPO record);
    
    /**
     * 根据ID更新
     */
    int updateById(BargainAssistRecordPO record);
    
    /**
     * 根据ID删除
     */
    int deleteById(@Param("id") Long id);
}
