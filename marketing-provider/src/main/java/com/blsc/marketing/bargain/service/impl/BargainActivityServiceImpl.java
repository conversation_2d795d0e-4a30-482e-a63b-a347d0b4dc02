package com.blsc.marketing.bargain.service.impl;

import com.blsc.commons.id.IdWorkerCommon;
import com.blsc.marketing.bargain.bean.bo.BargainAssistRecordBO;
import com.blsc.marketing.bargain.bean.bo.UserBargainActivityBO;
import com.blsc.marketing.bargain.bean.dto.AssistBargainDTO;
import com.blsc.marketing.bargain.bean.dto.QueryBargainActivityDTO;
import com.blsc.marketing.bargain.bean.dto.StartBargainActivityDTO;
import com.blsc.marketing.bargain.bean.po.BargainActivityConfigPO;
import com.blsc.marketing.bargain.bean.po.BargainAssistRecordPO;
import com.blsc.marketing.bargain.bean.po.UserBargainActivityPO;
import com.blsc.marketing.bargain.bean.po.UserDailyAssistLimitPO;
import com.blsc.marketing.bargain.mapper.BargainActivityConfigMapper;
import com.blsc.marketing.bargain.mapper.BargainAssistRecordMapper;
import com.blsc.marketing.bargain.mapper.UserBargainActivityMapper;
import com.blsc.marketing.bargain.mapper.UserDailyAssistLimitMapper;
import com.blsc.marketing.bargain.service.BargainActivityService;
import com.blsc.marketing.bargain.util.BargainAmountCalculator;
import com.blsc.marketing.bargain.util.BargainBeanConverter;
import com.blsc.marketing.coupon.bean.bo.PageBO;
import com.blsc.marketing.coupon.service.CouponCacheService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 助力活动服务实现
 */
@Slf4j
@Service
public class BargainActivityServiceImpl implements BargainActivityService {
    
    private static final int DAILY_ASSIST_LIMIT = 5; // 每日助力限制
    
    @Autowired
    private BargainActivityConfigMapper bargainActivityConfigMapper;
    
    @Autowired
    private UserBargainActivityMapper userBargainActivityMapper;
    
    @Autowired
    private BargainAssistRecordMapper bargainAssistRecordMapper;
    
    @Autowired
    private UserDailyAssistLimitMapper userDailyAssistLimitMapper;
    
    @Autowired
    private CouponCacheService couponCacheService;
    
    @Autowired
    private IdWorkerCommon idWorkerCommon;
    
    @Override
    @Transactional(rollbackFor = Exception.class)
    public UserBargainActivityBO startBargainActivity(StartBargainActivityDTO dto) {
        log.info("用户发起助力活动: userId={}, activityId={}, productId={}", 
                dto.getUserId(), dto.getActivityId(), dto.getProductId());
        
        try {
            // 1. 查询活动配置
            BargainActivityConfigPO config = bargainActivityConfigMapper.selectById(dto.getActivityId());
            if (config == null || config.getStatus() != 1) {
                throw new RuntimeException("活动配置不存在或已禁用");
            }
            
            // 2. 检查用户是否已经发起过该活动
            UserBargainActivityPO existing = userBargainActivityMapper.selectByUserAndActivity(
                    dto.getUserId(), dto.getActivityId());
            if (existing != null) {
                throw new RuntimeException("用户已经发起过该活动");
            }
            
            // 3. 创建用户助力活动
            UserBargainActivityPO userBargain = new UserBargainActivityPO();
            // TODO: 设置雪花ID
            userBargain.setId(null); 
            userBargain.setActivityId(dto.getActivityId());
            userBargain.setUserId(dto.getUserId());
            userBargain.setAppId(dto.getAppId());
            userBargain.setProductId(dto.getProductId());
            userBargain.setOriginalPrice(config.getOriginalPrice());
            userBargain.setFloorPrice(config.getFloorPrice());
            userBargain.setCurrentPrice(config.getOriginalPrice());
            userBargain.setTotalBargainAmount(0);
            userBargain.setAssistCount(0);
            userBargain.setMinAssistCount(config.getMinAssistCount());
            userBargain.setStatus(1); // 进行中
            userBargain.setExpireTime(LocalDateTime.now().plusHours(config.getActivityDurationHours()));
            userBargain.setCreateTime(LocalDateTime.now());
            userBargain.setUpdateTime(LocalDateTime.now());
            
            int result = userBargainActivityMapper.insertOne(userBargain);
            if (result <= 0) {
                throw new RuntimeException("创建助力活动失败");
            }
            
            log.info("助力活动创建成功: userBargainId={}", userBargain.getId());
            return BargainBeanConverter.convertToUserBargainActivityBO(userBargain);
            
        } catch (Exception e) {
            log.error("发起助力活动失败", e);
            throw new RuntimeException("发起助力活动失败: " + e.getMessage());
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public BargainAssistRecordBO assistBargain(AssistBargainDTO dto) {
        log.info("用户参与助力: assistUserId={}, userBargainId={}",
                dto.getAssistUserId(), dto.getUserBargainId());

        try {
            // 1. 检查是否可以助力
            BargainAssistCheckResult checkResult = checkCanAssist(
                    dto.getAssistUserId(), dto.getUserBargainId(), dto.getAppId());
            if (!checkResult.isCanAssist()) {
                throw new RuntimeException(checkResult.getReason());
            }

            // 2. 获取助力活动信息
            UserBargainActivityPO userBargain = userBargainActivityMapper.selectById(dto.getUserBargainId());
            if (userBargain == null) {
                throw new RuntimeException("助力活动不存在");
            }

            // 3. 使用分布式锁防止并发问题
            String lockKey = "bargain_assist_" + dto.getUserBargainId();
            Boolean lockAcquired = couponCacheService.tryLock(lockKey, 10);
            if (!lockAcquired) {
                throw new RuntimeException("系统繁忙，请稍后重试");
            }

            try {
                // 4. 重新查询最新状态
                userBargain = userBargainActivityMapper.selectById(dto.getUserBargainId());
                if (userBargain.getStatus() != 1) {
                    throw new RuntimeException("助力活动已结束");
                }

                // 5. 计算砍价金额（引入用户ID的随机性）
                int maxBargainAmount = userBargain.getOriginalPrice() - userBargain.getFloorPrice();
                int currentAssistOrder = userBargain.getAssistCount() + 1;
                int bargainAmount = BargainAmountCalculator.calculateBargainAmount(
                        currentAssistOrder, maxBargainAmount, userBargain.getMinAssistCount(),
                        userBargain.getTotalBargainAmount(), dto.getAssistUserId());

                // 6. 创建助力记录
                BargainAssistRecordPO assistRecord = new BargainAssistRecordPO();
                // TODO: 设置雪花ID
                assistRecord.setId(null);
                assistRecord.setUserBargainId(dto.getUserBargainId());
                assistRecord.setAssistUserId(dto.getAssistUserId());
                assistRecord.setInitiatorUserId(userBargain.getUserId());
                assistRecord.setBargainAmount(bargainAmount);
                assistRecord.setAssistOrder(currentAssistOrder);
                assistRecord.setAppId(dto.getAppId());
                assistRecord.setCreateTime(LocalDateTime.now());

                int recordResult = bargainAssistRecordMapper.insertOne(assistRecord);
                if (recordResult <= 0) {
                    throw new RuntimeException("创建助力记录失败");
                }

                // 7. 更新助力活动状态
                userBargain.setAssistCount(currentAssistOrder);
                userBargain.setTotalBargainAmount(userBargain.getTotalBargainAmount() + bargainAmount);
                userBargain.setCurrentPrice(userBargain.getOriginalPrice() - userBargain.getTotalBargainAmount());
                userBargain.setUpdateTime(LocalDateTime.now());

                // 8. 检查是否达到解锁条件
                boolean reachedMinCount = currentAssistOrder >= userBargain.getMinAssistCount();
                boolean reachedMinAmount = userBargain.getTotalBargainAmount() >= maxBargainAmount;
                if (reachedMinCount && reachedMinAmount) {
                    userBargain.setStatus(2); // 成功，可购买
                    userBargain.setSuccessTime(LocalDateTime.now());
                }

                int updateResult = userBargainActivityMapper.updateById(userBargain);
                if (updateResult <= 0) {
                    throw new RuntimeException("更新助力活动失败");
                }

                // 9. 更新用户每日助力次数
                updateUserDailyAssistCount(dto.getAssistUserId(), dto.getAppId());

                log.info("助力成功: assistRecordId={}, bargainAmount={}, currentPrice={}",
                        assistRecord.getId(), bargainAmount, userBargain.getCurrentPrice());

                return BargainBeanConverter.convertToBargainAssistRecordBO(assistRecord);

            } finally {
                couponCacheService.releaseLock(lockKey);
            }

        } catch (Exception e) {
            log.error("参与助力失败", e);
            throw new RuntimeException("参与助力失败: " + e.getMessage());
        }
    }

    @Override
    public PageBO<UserBargainActivityBO> queryUserBargainActivities(QueryBargainActivityDTO dto) {
        log.info("查询用户助力活动列表: userId={}, status={}", dto.getUserId(), dto.getStatus());

        try {
            int offset = (dto.getPageNum() - 1) * dto.getPageSize();
            List<UserBargainActivityPO> activities = userBargainActivityMapper.selectByUserWithPage(
                    dto.getUserId(), dto.getAppId(), dto.getStatus(), offset, dto.getPageSize());

            int total = userBargainActivityMapper.countByUser(dto.getUserId(), dto.getAppId(), dto.getStatus());

            List<UserBargainActivityBO> activityBOs = BargainBeanConverter.convertToUserBargainActivityBOList(activities);

            // 填充额外信息
            for (UserBargainActivityBO activityBO : activityBOs) {
                fillBargainActivityExtraInfo(activityBO);
            }

            return new PageBO<>(activityBOs, total, dto.getPageNum(), dto.getPageSize());

        } catch (Exception e) {
            log.error("查询用户助力活动列表失败", e);
            throw new RuntimeException("查询助力活动列表失败: " + e.getMessage());
        }
    }

    @Override
    public UserBargainActivityBO getBargainActivityById(Long userBargainId, boolean includeRecords) {
        log.info("查询助力活动详情: userBargainId={}, includeRecords={}", userBargainId, includeRecords);

        try {
            UserBargainActivityPO userBargain = userBargainActivityMapper.selectById(userBargainId);
            if (userBargain == null) {
                throw new RuntimeException("助力活动不存在");
            }

            UserBargainActivityBO activityBO = BargainBeanConverter.convertToUserBargainActivityBO(userBargain);

            // 填充额外信息
            fillBargainActivityExtraInfo(activityBO);

            // 如果需要包含助力记录
            if (includeRecords) {
                List<BargainAssistRecordPO> records = bargainAssistRecordMapper.selectByUserBargainId(userBargainId);
                List<BargainAssistRecordBO> recordBOs = BargainBeanConverter.convertToBargainAssistRecordBOList(records);
                activityBO.setAssistRecords(recordBOs);
            }

            return activityBO;

        } catch (Exception e) {
            log.error("查询助力活动详情失败", e);
            throw new RuntimeException("查询助力活动详情失败: " + e.getMessage());
        }
    }

    @Override
    public BargainAssistCheckResult checkCanAssist(String assistUserId, Long userBargainId, String appId) {
        try {
            // 1. 查询助力活动
            UserBargainActivityPO userBargain = userBargainActivityMapper.selectById(userBargainId);
            if (userBargain == null) {
                return BargainAssistCheckResult.fail("助力活动不存在");
            }

            // 2. 检查活动状态
            if (userBargain.getStatus() != 1) {
                return BargainAssistCheckResult.fail("助力活动已结束");
            }

            // 3. 检查是否过期
            if (LocalDateTime.now().isAfter(userBargain.getExpireTime())) {
                return BargainAssistCheckResult.fail("助力活动已过期");
            }

            // 4. 检查是否是自己的活动
            if (assistUserId.equals(userBargain.getUserId())) {
                return BargainAssistCheckResult.fail("不能助力自己的活动");
            }

            // 5. 检查是否已经助力过
            BargainAssistRecordPO existingRecord = bargainAssistRecordMapper.selectByUserBargainAndAssistUser(
                    userBargainId, assistUserId);
            if (existingRecord != null) {
                return BargainAssistCheckResult.fail("已经助力过该活动");
            }

            // 6. 检查每日助力次数限制
            Integer dailyCount = getUserDailyAssistCount(assistUserId, appId);
            if (dailyCount >= DAILY_ASSIST_LIMIT) {
                return BargainAssistCheckResult.fail("今日助力次数已达上限");
            }

            return BargainAssistCheckResult.success();

        } catch (Exception e) {
            log.error("检查助力权限失败", e);
            return BargainAssistCheckResult.fail("检查助力权限失败");
        }
    }

    @Override
    public Integer getUserDailyRemainingAssistCount(String userId, String appId) {
        try {
            Integer dailyCount = getUserDailyAssistCount(userId, appId);
            return Math.max(0, DAILY_ASSIST_LIMIT - dailyCount);
        } catch (Exception e) {
            log.error("获取用户每日剩余助力次数失败", e);
            return 0;
        }
    }

    @Override
    public Integer expireBargainActivities() {
        log.info("开始批量过期助力活动");

        try {
            int expiredCount = userBargainActivityMapper.expireActivities(LocalDateTime.now());
            log.info("批量过期助力活动完成，过期数量: {}", expiredCount);
            return expiredCount;
        } catch (Exception e) {
            log.error("批量过期助力活动失败", e);
            return 0;
        }
    }

    /**
     * 获取用户今日助力次数
     */
    private Integer getUserDailyAssistCount(String userId, String appId) {
        LocalDate today = LocalDate.now();
        UserDailyAssistLimitPO limitRecord = userDailyAssistLimitMapper.selectByUserAndDate(userId, today, appId);
        return limitRecord != null ? limitRecord.getAssistCount() : 0;
    }

    /**
     * 更新用户每日助力次数
     */
    private void updateUserDailyAssistCount(String userId, String appId) {
        LocalDate today = LocalDate.now();
        UserDailyAssistLimitPO limitRecord = userDailyAssistLimitMapper.selectByUserAndDate(userId, today, appId);

        if (limitRecord == null) {
            // 创建新记录
            limitRecord = new UserDailyAssistLimitPO();
            // TODO: 设置雪花ID
            limitRecord.setId(null);
            limitRecord.setUserId(userId);
            limitRecord.setAssistDate(today);
            limitRecord.setAssistCount(1);
            limitRecord.setAppId(appId);
            limitRecord.setCreateTime(LocalDateTime.now());
            limitRecord.setUpdateTime(LocalDateTime.now());
            userDailyAssistLimitMapper.insertOne(limitRecord);
        } else {
            // 更新计数
            limitRecord.setAssistCount(limitRecord.getAssistCount() + 1);
            limitRecord.setUpdateTime(LocalDateTime.now());
            userDailyAssistLimitMapper.updateById(limitRecord);
        }
    }

    /**
     * 填充助力活动额外信息
     */
    private void fillBargainActivityExtraInfo(UserBargainActivityBO activityBO) {
        // 计算是否可以购买
        boolean reachedMinCount = activityBO.getAssistCount() >= activityBO.getMinAssistCount();
        int maxBargainAmount = activityBO.getOriginalPrice() - activityBO.getFloorPrice();
        boolean reachedMinAmount = activityBO.getTotalBargainAmount() >= maxBargainAmount;
        activityBO.setCanPurchase(reachedMinCount && reachedMinAmount);

        // 计算剩余助力人数
        activityBO.setRemainingAssistCount(Math.max(0, activityBO.getMinAssistCount() - activityBO.getAssistCount()));

        // 计算剩余砍价金额
        activityBO.setRemainingBargainAmount(Math.max(0, maxBargainAmount - activityBO.getTotalBargainAmount()));
    }
}
