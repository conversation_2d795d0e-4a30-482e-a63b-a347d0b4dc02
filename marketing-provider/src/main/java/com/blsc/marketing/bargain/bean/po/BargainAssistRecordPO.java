package com.blsc.marketing.bargain.bean.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 助力记录实体类
 * 对应数据库表：bargain_assist_record
 */
@Data
public class BargainAssistRecordPO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户助力活动ID
     */
    private Long userBargainId;
    
    /**
     * 助力用户ID
     */
    private String assistUserId;
    
    /**
     * 发起用户ID
     */
    private String initiatorUserId;
    
    /**
     * 本次砍价金额(分)
     */
    private Integer bargainAmount;
    
    /**
     * 助力顺序(第几个助力)
     */
    private Integer assistOrder;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
}
