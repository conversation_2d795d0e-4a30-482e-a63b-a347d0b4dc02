package com.blsc.marketing.bargain.bean.po;

import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户每日助力限制实体类
 * 对应数据库表：user_daily_assist_limit
 */
@Data
public class UserDailyAssistLimitPO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 用户ID
     */
    private String userId;
    
    /**
     * 助力日期
     */
    private LocalDate assistDate;
    
    /**
     * 当日助力次数
     */
    private Integer assistCount;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
