package com.blsc.marketing.bargain.bean.dto;

import lombok.Data;

/**
 * 查询助力活动DTO
 */
@Data
public class QueryBargainActivityDTO {

    /**
     * 用户ID
     */
    private String userId;

    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 状态筛选：1-进行中，2-成功(可购买)，3-失败，4-已购买
     */
    private Integer status;
    
    /**
     * 页码
     */
    private Integer pageNum = 1;
    
    /**
     * 页大小
     */
    private Integer pageSize = 10;
}
