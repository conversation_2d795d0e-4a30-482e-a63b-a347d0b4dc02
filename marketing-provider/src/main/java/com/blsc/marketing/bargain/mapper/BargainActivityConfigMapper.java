package com.blsc.marketing.bargain.mapper;

import com.blsc.marketing.bargain.bean.po.BargainActivityConfigPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 助力活动配置Mapper
 */
@Mapper
public interface BargainActivityConfigMapper {
    
    /**
     * 根据ID查询
     */
    BargainActivityConfigPO selectById(@Param("id") Long id);
    
    /**
     * 根据应用ID和商品ID查询
     */
    BargainActivityConfigPO selectByAppAndProduct(@Param("appId") String appId, @Param("productId") String productId);
    
    /**
     * 查询启用的活动配置列表
     */
    List<BargainActivityConfigPO> selectActiveConfigs(@Param("appId") String appId);
    
    /**
     * 插入一条记录
     */
    int insertOne(BargainActivityConfigPO config);
    
    /**
     * 根据ID更新
     */
    int updateById(BargainActivityConfigPO config);
    
    /**
     * 根据ID删除
     */
    int deleteById(@Param("id") Long id);
}
