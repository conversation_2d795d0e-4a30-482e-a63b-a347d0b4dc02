package com.blsc.marketing.bargain.mapper;

import com.blsc.marketing.bargain.bean.po.UserBargainActivityPO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户助力活动Mapper
 */
@Mapper
public interface UserBargainActivityMapper {
    
    /**
     * 根据ID查询
     */
    UserBargainActivityPO selectById(@Param("id") Long id);
    
    /**
     * 根据用户ID和活动ID查询
     */
    UserBargainActivityPO selectByUserAndActivity(@Param("userId") String userId, @Param("activityId") Long activityId);
    
    /**
     * 分页查询用户助力活动
     */
    List<UserBargainActivityPO> selectByUserWithPage(@Param("userId") String userId, 
                                                     @Param("appId") String appId,
                                                     @Param("status") Integer status,
                                                     @Param("offset") Integer offset, 
                                                     @Param("pageSize") Integer pageSize);
    
    /**
     * 统计用户助力活动数量
     */
    int countByUser(@Param("userId") String userId, @Param("appId") String appId, @Param("status") Integer status);
    
    /**
     * 插入一条记录
     */
    int insertOne(UserBargainActivityPO userBargain);
    
    /**
     * 根据ID更新
     */
    int updateById(UserBargainActivityPO userBargain);
    
    /**
     * 批量过期活动
     */
    int expireActivities(@Param("currentTime") LocalDateTime currentTime);
    
    /**
     * 根据ID删除
     */
    int deleteById(@Param("id") Long id);
}
