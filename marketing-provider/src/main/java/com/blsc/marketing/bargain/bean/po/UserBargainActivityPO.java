package com.blsc.marketing.bargain.bean.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户助力活动实体类
 * 对应数据库表：user_bargain_activity
 */
@Data
public class UserBargainActivityPO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 活动配置ID
     */
    private Long activityId;
    
    /**
     * 发起用户ID
     */
    private String userId;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 商品ID
     */
    private String productId;
    
    /**
     * 商品原价(分)
     */
    private Integer originalPrice;
    
    /**
     * 底价金额(分)
     */
    private Integer floorPrice;
    
    /**
     * 当前价格(分)
     */
    private Integer currentPrice;
    
    /**
     * 已砍价总金额(分)
     */
    private Integer totalBargainAmount;
    
    /**
     * 当前助力人数
     */
    private Integer assistCount;
    
    /**
     * 最少助力人数阈值
     */
    private Integer minAssistCount;
    
    /**
     * 状态：1-进行中，2-成功(可购买)，3-失败，4-已购买
     */
    private Integer status;
    
    /**
     * 活动过期时间
     */
    private LocalDateTime expireTime;
    
    /**
     * 成功解锁时间
     */
    private LocalDateTime successTime;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
