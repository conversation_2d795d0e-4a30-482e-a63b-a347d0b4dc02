package com.blsc.marketing.bargain.util;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 砍价金额计算器
 * 
 * 算法说明：
 * 1. 使用指数衰减函数确保前期砍价金额大，后期逐渐减少
 * 2. 严格保证N次砍价总额不超过设定的最大砍价金额
 * 3. 当砍价次数达到阈值时，总砍价金额接近但不超过最大值
 */
@Slf4j
public class BargainAmountCalculator {
    
    /**
     * 最小砍价金额(分)
     */
    private static final int MIN_BARGAIN_AMOUNT = 1;
    
    /**
     * 计算单次砍价金额
     * 
     * @param currentAssistOrder 当前助力顺序(从1开始)
     * @param maxBargainAmount 最大砍价总金额(分)
     * @param minAssistCount 最少助力人数阈值
     * @param alreadyBargainAmount 已砍价总金额(分)
     * @return 本次砍价金额(分)
     */
    public static int calculateBargainAmount(int currentAssistOrder, int maxBargainAmount, 
                                           int minAssistCount, int alreadyBargainAmount) {
        
        if (currentAssistOrder <= 0 || maxBargainAmount <= 0 || minAssistCount <= 0) {
            log.warn("砍价参数无效: currentAssistOrder={}, maxBargainAmount={}, minAssistCount={}", 
                    currentAssistOrder, maxBargainAmount, minAssistCount);
            return MIN_BARGAIN_AMOUNT;
        }
        
        // 如果已经砍价金额超过最大值，返回最小值
        if (alreadyBargainAmount >= maxBargainAmount) {
            return MIN_BARGAIN_AMOUNT;
        }
        
        // 计算剩余可砍价金额
        int remainingAmount = maxBargainAmount - alreadyBargainAmount;
        
        // 使用改进的指数衰减函数计算砍价金额
        // 公式: amount = remainingAmount * (1 - (currentOrder-1)/minAssistCount)^1.5
        double ratio = Math.max(0, 1.0 - (double)(currentAssistOrder - 1) / minAssistCount);
        double decayFactor = Math.pow(ratio, 1.5); // 1.5次方衰减，平衡前后期砍价

        // 基础砍价金额，提高系数以增加利用率
        int baseAmount = (int) Math.round(remainingAmount * decayFactor * 0.6); // 60%的衰减系数
        
        // 确保砍价金额在合理范围内
        int bargainAmount = Math.max(MIN_BARGAIN_AMOUNT, baseAmount);
        
        // 确保不超过剩余金额
        bargainAmount = Math.min(bargainAmount, remainingAmount);
        
        // 如果是最后几次助力，确保能够达到阈值
        if (currentAssistOrder >= minAssistCount) {
            // 达到阈值后，给予较小的随机砍价金额
            bargainAmount = Math.min(bargainAmount, remainingAmount / 10 + MIN_BARGAIN_AMOUNT);
        }
        
        log.debug("砍价计算: 第{}次助力, 最大砍价={}, 已砍价={}, 剩余={}, 本次砍价={}", 
                currentAssistOrder, maxBargainAmount, alreadyBargainAmount, remainingAmount, bargainAmount);
        
        return bargainAmount;
    }
    
    /**
     * 预计算砍价分布
     * 用于验证算法的正确性
     * 
     * @param maxBargainAmount 最大砍价总金额(分)
     * @param minAssistCount 最少助力人数阈值
     * @return 每次砍价金额列表
     */
    public static List<Integer> preCalculateBargainDistribution(int maxBargainAmount, int minAssistCount) {
        List<Integer> distribution = new ArrayList<>();
        int totalBargained = 0;
        
        for (int i = 1; i <= minAssistCount + 5; i++) { // 多计算5次，验证超过阈值的情况
            int bargainAmount = calculateBargainAmount(i, maxBargainAmount, minAssistCount, totalBargained);
            distribution.add(bargainAmount);
            totalBargained += bargainAmount;
            
            if (totalBargained >= maxBargainAmount) {
                break;
            }
        }
        
        return distribution;
    }
    
    /**
     * 验证砍价算法是否满足要求
     * 
     * @param maxBargainAmount 最大砍价总金额(分)
     * @param minAssistCount 最少助力人数阈值
     * @return 验证结果
     */
    public static BargainValidationResult validateBargainAlgorithm(int maxBargainAmount, int minAssistCount) {
        List<Integer> distribution = preCalculateBargainDistribution(maxBargainAmount, minAssistCount);
        
        int totalAtThreshold = 0;
        for (int i = 0; i < Math.min(minAssistCount, distribution.size()); i++) {
            totalAtThreshold += distribution.get(i);
        }
        
        boolean isValid = totalAtThreshold <= maxBargainAmount;
        double utilizationRate = (double) totalAtThreshold / maxBargainAmount;
        
        return new BargainValidationResult(isValid, totalAtThreshold, utilizationRate, distribution);
    }
    
    /**
     * 砍价验证结果
     */
    public static class BargainValidationResult {
        private final boolean valid;
        private final int totalBargainedAtThreshold;
        private final double utilizationRate;
        private final List<Integer> distribution;
        
        public BargainValidationResult(boolean valid, int totalBargainedAtThreshold, 
                                     double utilizationRate, List<Integer> distribution) {
            this.valid = valid;
            this.totalBargainedAtThreshold = totalBargainedAtThreshold;
            this.utilizationRate = utilizationRate;
            this.distribution = distribution;
        }
        
        public boolean isValid() { return valid; }
        public int getTotalBargainedAtThreshold() { return totalBargainedAtThreshold; }
        public double getUtilizationRate() { return utilizationRate; }
        public List<Integer> getDistribution() { return distribution; }
        
        @Override
        public String toString() {
            return String.format("BargainValidationResult{valid=%s, totalAtThreshold=%d, utilizationRate=%.2f%%, distribution=%s}", 
                    valid, totalBargainedAtThreshold, utilizationRate * 100, distribution);
        }
    }
}
