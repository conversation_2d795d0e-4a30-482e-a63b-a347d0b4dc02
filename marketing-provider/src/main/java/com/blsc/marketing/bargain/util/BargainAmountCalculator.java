package com.blsc.marketing.bargain.util;

import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

/**
 * 砍价金额计算器
 * 
 * 算法说明：
 * 1. 使用指数衰减函数确保前期砍价金额大，后期逐渐减少
 * 2. 严格保证N次砍价总额不超过设定的最大砍价金额
 * 3. 当砍价次数达到阈值时，总砍价金额接近但不超过最大值
 */
@Slf4j
public class BargainAmountCalculator {
    
    /**
     * 最小砍价金额(分)
     */
    private static final int MIN_BARGAIN_AMOUNT = 1;
    
    /**
     * 计算单次砍价金额
     *
     * @param currentAssistOrder 当前助力顺序(从1开始)
     * @param maxBargainAmount 最大砍价总金额(分)
     * @param minAssistCount 最少助力人数阈值
     * @param alreadyBargainAmount 已砍价总金额(分)
     * @param randomSeed 随机种子，由业务层提供，确保每次助力的随机性
     * @return 本次砍价金额(分)
     */
    public static int calculateBargainAmount(int currentAssistOrder, int maxBargainAmount,
                                           int minAssistCount, int alreadyBargainAmount, long randomSeed) {

        if (currentAssistOrder <= 0 || maxBargainAmount <= 0 || minAssistCount <= 0) {
            log.warn("砍价参数无效: currentAssistOrder={}, maxBargainAmount={}, minAssistCount={}",
                    currentAssistOrder, maxBargainAmount, minAssistCount);
            return MIN_BARGAIN_AMOUNT;
        }

        // 如果已经砍价金额超过最大值，返回最小值
        if (alreadyBargainAmount >= maxBargainAmount) {
            return MIN_BARGAIN_AMOUNT;
        }

        // 计算剩余可砍价金额
        int remainingAmount = maxBargainAmount - alreadyBargainAmount;

        // 使用业务层提供的随机种子创建随机数生成器
        // 业务层可以根据具体需求生成不同的随机种子，确保每次助力都有随机性
        Random random = new Random(randomSeed);

        int bargainAmount;

        if (currentAssistOrder <= minAssistCount) {
            // 阈值内：使用指数衰减函数，确保前期砍价多，后期砍价少
            // 预留更多金额给阈值后的助力，确保阈值后2-3次就能完成
            int reservedAmount = (int) (maxBargainAmount * 0.25); // 预留25%给阈值后
            int availableAmount = maxBargainAmount - reservedAmount - alreadyBargainAmount;
            availableAmount = Math.max(0, availableAmount);

            if (availableAmount > 0) {
                double ratio = Math.max(0, 1.0 - (double)(currentAssistOrder - 1) / minAssistCount);
                double decayFactor = Math.pow(ratio, 1.2); // 1.2次方衰减

                // 基础砍价金额
                int baseAmount = (int) Math.round(availableAmount * decayFactor * 0.7);

                // 引入随机性：在基础金额的80%-120%之间波动
                double randomFactor = 0.8 + random.nextDouble() * 0.4; // 0.8 到 1.2
                bargainAmount = (int) Math.round(baseAmount * randomFactor);
            } else {
                bargainAmount = MIN_BARGAIN_AMOUNT;
            }

        } else {
            // 阈值后：大幅提高砍价金额，确保2-3次就能完成
            int assistAfterThreshold = currentAssistOrder - minAssistCount; // 超过阈值的次数

            double baseRatio;
            if (assistAfterThreshold == 1) {
                // 第一次超过阈值：基础比例80%，随机波动75%-85%
                baseRatio = 0.75 + random.nextDouble() * 0.1; // 75% - 85%
            } else if (assistAfterThreshold == 2) {
                // 第二次超过阈值：基础比例90%，随机波动85%-95%
                baseRatio = 0.85 + random.nextDouble() * 0.1; // 85% - 95%
            } else {
                // 第三次及以后：基础比例95%，随机波动90%-100%
                baseRatio = 0.9 + random.nextDouble() * 0.1; // 90% - 100%
            }

            bargainAmount = (int) Math.round(remainingAmount * baseRatio);
        }

        // 确保砍价金额在合理范围内
        bargainAmount = Math.max(MIN_BARGAIN_AMOUNT, bargainAmount);

        // 关键逻辑：如果本次砍价会超过最大砍价金额，则精确砍到最大值
        if (alreadyBargainAmount + bargainAmount > maxBargainAmount) {
            bargainAmount = maxBargainAmount - alreadyBargainAmount;
            log.info("精确砍价: 第{}次助力将达到最大砍价金额，调整砍价金额为{}分", currentAssistOrder, bargainAmount);
        }

        // 确保不超过剩余金额
        bargainAmount = Math.min(bargainAmount, remainingAmount);

        log.debug("砍价计算: 第{}次助力, 最大砍价={}, 已砍价={}, 剩余={}, 本次砍价={}, 阈值={}",
                currentAssistOrder, maxBargainAmount, alreadyBargainAmount, remainingAmount,
                bargainAmount, minAssistCount);

        return bargainAmount;
    }


    
    /**
     * 预计算砍价分布
     * 用于验证算法的正确性
     *
     * @param maxBargainAmount 最大砍价总金额(分)
     * @param minAssistCount 最少助力人数阈值
     * @param baseSeed 基础随机种子
     * @return 每次砍价金额列表
     */
    public static List<Integer> preCalculateBargainDistribution(int maxBargainAmount, int minAssistCount, long baseSeed) {
        List<Integer> distribution = new ArrayList<>();
        int totalBargained = 0;

        for (int i = 1; i <= minAssistCount + 5; i++) { // 多计算5次，验证超过阈值的情况
            // 为每次助力生成不同的随机种子
            long randomSeed = baseSeed + i * 1000L;
            int bargainAmount = calculateBargainAmount(i, maxBargainAmount, minAssistCount, totalBargained, randomSeed);
            distribution.add(bargainAmount);
            totalBargained += bargainAmount;

            if (totalBargained >= maxBargainAmount) {
                break;
            }
        }

        return distribution;
    }
    
    /**
     * 验证砍价算法是否满足要求
     *
     * @param maxBargainAmount 最大砍价总金额(分)
     * @param minAssistCount 最少助力人数阈值
     * @param baseSeed 基础随机种子
     * @return 验证结果
     */
    public static BargainValidationResult validateBargainAlgorithm(int maxBargainAmount, int minAssistCount, long baseSeed) {
        List<Integer> distribution = preCalculateBargainDistribution(maxBargainAmount, minAssistCount, baseSeed);

        int totalAtThreshold = 0;
        for (int i = 0; i < Math.min(minAssistCount, distribution.size()); i++) {
            totalAtThreshold += distribution.get(i);
        }

        boolean isValid = totalAtThreshold <= maxBargainAmount;
        double utilizationRate = (double) totalAtThreshold / maxBargainAmount;

        return new BargainValidationResult(isValid, totalAtThreshold, utilizationRate, distribution);
    }
    
    /**
     * 砍价验证结果
     */
    public static class BargainValidationResult {
        private final boolean valid;
        private final int totalBargainedAtThreshold;
        private final double utilizationRate;
        private final List<Integer> distribution;
        
        public BargainValidationResult(boolean valid, int totalBargainedAtThreshold, 
                                     double utilizationRate, List<Integer> distribution) {
            this.valid = valid;
            this.totalBargainedAtThreshold = totalBargainedAtThreshold;
            this.utilizationRate = utilizationRate;
            this.distribution = distribution;
        }
        
        public boolean isValid() { return valid; }
        public int getTotalBargainedAtThreshold() { return totalBargainedAtThreshold; }
        public double getUtilizationRate() { return utilizationRate; }
        public List<Integer> getDistribution() { return distribution; }
        
        @Override
        public String toString() {
            return String.format("BargainValidationResult{valid=%s, totalAtThreshold=%d, utilizationRate=%.2f%%, distribution=%s}", 
                    valid, totalBargainedAtThreshold, utilizationRate * 100, distribution);
        }
    }
}
