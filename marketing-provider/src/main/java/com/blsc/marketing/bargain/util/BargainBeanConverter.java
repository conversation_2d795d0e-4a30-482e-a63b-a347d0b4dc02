package com.blsc.marketing.bargain.util;

import com.blsc.marketing.bargain.bean.bo.BargainAssistRecordBO;
import com.blsc.marketing.bargain.bean.bo.UserBargainActivityBO;
import com.blsc.marketing.bargain.bean.po.BargainAssistRecordPO;
import com.blsc.marketing.bargain.bean.po.UserBargainActivityPO;

import java.util.ArrayList;
import java.util.List;

/**
 * 助力活动Bean转换工具类
 */
public class BargainBeanConverter {
    
    /**
     * 转换用户助力活动PO为BO
     */
    public static UserBargainActivityBO convertToUserBargainActivityBO(UserBargainActivityPO po) {
        if (po == null) {
            return null;
        }
        
        UserBargainActivityBO bo = new UserBargainActivityBO();
        bo.setId(po.getId());
        bo.setActivityId(po.getActivityId());
        bo.setUserId(po.getUserId());
        bo.setAppId(po.getAppId());
        bo.setProductId(po.getProductId());
        bo.setOriginalPrice(po.getOriginalPrice());
        bo.setFloorPrice(po.getFloorPrice());
        bo.setCurrentPrice(po.getCurrentPrice());
        bo.setTotalBargainAmount(po.getTotalBargainAmount());
        bo.setAssistCount(po.getAssistCount());
        bo.setMinAssistCount(po.getMinAssistCount());
        bo.setStatus(po.getStatus());
        bo.setExpireTime(po.getExpireTime());
        bo.setSuccessTime(po.getSuccessTime());
        bo.setCreateTime(po.getCreateTime());
        bo.setUpdateTime(po.getUpdateTime());
        
        return bo;
    }
    
    /**
     * 转换用户助力活动PO列表为BO列表
     */
    public static List<UserBargainActivityBO> convertToUserBargainActivityBOList(List<UserBargainActivityPO> poList) {
        if (poList == null || poList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<UserBargainActivityBO> boList = new ArrayList<>();
        for (UserBargainActivityPO po : poList) {
            UserBargainActivityBO bo = convertToUserBargainActivityBO(po);
            if (bo != null) {
                boList.add(bo);
            }
        }
        
        return boList;
    }
    
    /**
     * 转换助力记录PO为BO
     */
    public static BargainAssistRecordBO convertToBargainAssistRecordBO(BargainAssistRecordPO po) {
        if (po == null) {
            return null;
        }
        
        BargainAssistRecordBO bo = new BargainAssistRecordBO();
        bo.setId(po.getId());
        bo.setUserBargainId(po.getUserBargainId());
        bo.setAssistUserId(po.getAssistUserId());
        bo.setInitiatorUserId(po.getInitiatorUserId());
        bo.setBargainAmount(po.getBargainAmount());
        bo.setAssistOrder(po.getAssistOrder());
        bo.setAppId(po.getAppId());
        bo.setCreateTime(po.getCreateTime());
        
        return bo;
    }
    
    /**
     * 转换助力记录PO列表为BO列表
     */
    public static List<BargainAssistRecordBO> convertToBargainAssistRecordBOList(List<BargainAssistRecordPO> poList) {
        if (poList == null || poList.isEmpty()) {
            return new ArrayList<>();
        }
        
        List<BargainAssistRecordBO> boList = new ArrayList<>();
        for (BargainAssistRecordPO po : poList) {
            BargainAssistRecordBO bo = convertToBargainAssistRecordBO(po);
            if (bo != null) {
                boList.add(bo);
            }
        }
        
        return boList;
    }
}
