package com.blsc.marketing.bargain.service;

import com.blsc.marketing.bargain.bean.bo.UserBargainActivityBO;
import com.blsc.marketing.bargain.bean.bo.BargainAssistRecordBO;
import com.blsc.marketing.bargain.bean.dto.StartBargainActivityDTO;
import com.blsc.marketing.bargain.bean.dto.AssistBargainDTO;
import com.blsc.marketing.bargain.bean.dto.QueryBargainActivityDTO;
import com.blsc.marketing.coupon.bean.bo.PageBO;

import java.util.List;

/**
 * 助力活动服务接口
 */
public interface BargainActivityService {
    
    /**
     * 发起助力活动
     *
     * @param dto 发起DTO
     * @return 助力活动信息
     */
    UserBargainActivityBO startBargainActivity(StartBargainActivityDTO dto);
    
    /**
     * 参与助力
     *
     * @param dto 助力DTO
     * @return 助力记录
     */
    BargainAssistRecordBO assistBargain(AssistBargainDTO dto);
    
    /**
     * 查询用户助力活动列表
     *
     * @param dto 查询DTO
     * @return 分页结果
     */
    PageBO<UserBargainActivityBO> queryUserBargainActivities(QueryBargainActivityDTO dto);
    
    /**
     * 根据ID查询助力活动详情
     *
     * @param userBargainId 用户助力活动ID
     * @param includeRecords 是否包含助力记录
     * @return 助力活动详情
     */
    UserBargainActivityBO getBargainActivityById(Long userBargainId, boolean includeRecords);
    
    /**
     * 检查用户是否可以助力
     *
     * @param assistUserId 助力用户ID
     * @param userBargainId 用户助力活动ID
     * @param appId 应用ID
     * @return 检查结果
     */
    BargainAssistCheckResult checkCanAssist(String assistUserId, Long userBargainId, String appId);
    
    /**
     * 获取用户今日剩余助力次数
     *
     * @param userId 用户ID
     * @param appId 应用ID
     * @return 剩余次数
     */
    Integer getUserDailyRemainingAssistCount(String userId, String appId);
    
    /**
     * 批量过期助力活动
     *
     * @return 过期数量
     */
    Integer expireBargainActivities();
    
    /**
     * 助力检查结果
     */
    class BargainAssistCheckResult {
        private final boolean canAssist;
        private final String reason;
        
        public BargainAssistCheckResult(boolean canAssist, String reason) {
            this.canAssist = canAssist;
            this.reason = reason;
        }
        
        public boolean isCanAssist() { return canAssist; }
        public String getReason() { return reason; }
        
        public static BargainAssistCheckResult success() {
            return new BargainAssistCheckResult(true, "可以助力");
        }
        
        public static BargainAssistCheckResult fail(String reason) {
            return new BargainAssistCheckResult(false, reason);
        }
    }
}
