package com.blsc.marketing.bargain.bean.po;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 助力活动配置实体类
 * 对应数据库表：bargain_activity_config
 */
@Data
public class BargainActivityConfigPO {
    
    /**
     * 主键ID
     */
    private Long id;
    
    /**
     * 活动名称
     */
    private String activityName;
    
    /**
     * 应用ID
     */
    private String appId;
    
    /**
     * 商品ID
     */
    private String productId;
    
    /**
     * 商品原价(分)
     */
    private Integer originalPrice;
    
    /**
     * 底价金额(分)
     */
    private Integer floorPrice;
    
    /**
     * 最少助力人数阈值
     */
    private Integer minAssistCount;
    
    /**
     * 最大助力人数限制
     */
    private Integer maxAssistCount;
    
    /**
     * 活动持续时间(小时)
     */
    private Integer activityDurationHours;
    
    /**
     * 状态：1-启用，0-禁用
     */
    private Integer status;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间
     */
    private LocalDateTime updateTime;
}
