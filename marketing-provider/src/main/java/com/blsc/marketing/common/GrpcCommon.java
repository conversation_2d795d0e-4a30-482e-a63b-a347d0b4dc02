package com.blsc.marketing.common;

import com.blsc.marketing.exception.BizException;
import com.blsc.marketing.exception.MarketingServiceCode;
import com.google.protobuf.BoolValue;
import com.google.rpc.Code;
import io.grpc.Metadata;
import io.grpc.Status;
import io.grpc.StatusRuntimeException;

import java.util.Optional;

/**
 * gRPC 常用工具类，处理异常转换和数据处理
 */
public class GrpcCommon {

    /**
     * 业务状态码存放的 gRPC metadata key
     */
    private static final Metadata.Key<String> ERROR_CODE_KEY = Metadata.Key.of("error-code", Metadata.ASCII_STRING_MARSHALLER);

    /**
     * 错误码类型枚举
     */
    private enum ErrorType {
        NOT_FOUND(Status.NOT_FOUND, 
                Integer.parseInt(MarketingServiceCode.NOT_FIND.getValue()),
                Integer.parseInt(MarketingServiceCode.NOT_FIND_PLACEHOLDER.getValue())),
        
        FAILED_PRECONDITION(Status.FAILED_PRECONDITION,
                Integer.parseInt(MarketingServiceCode.FAILED_PRECONDITION.getValue()),
                Integer.parseInt(MarketingServiceCode.FAILED_PRECONDITION_PLACEHOLDER.getValue())),
        
        INVALID_ARGUMENT(Status.INVALID_ARGUMENT,
                Integer.parseInt(MarketingServiceCode.INVALID_ARGUMENT.getValue()),
                Integer.parseInt(MarketingServiceCode.INVALID_ARGUMENT_PLACEHOLDER.getValue())),
        
        OUT_SERVICE_ERROR(Status.INTERNAL,
                Integer.parseInt(MarketingServiceCode.OUT_SERVICE_ERROR.getValue()),
                Integer.parseInt(MarketingServiceCode.OUT_SERVICE_PLACEHOLDER.getValue())),
        
        INTERNAL_ERROR(Status.INTERNAL,
                Integer.parseInt(MarketingServiceCode.INTERNAL_ERROR.getValue()),
                Integer.parseInt(MarketingServiceCode.INTERNAL_ERROR_PLACEHOLDER.getValue()));

        private final Status status;
        private final int minCode;
        private final int maxCode;

        ErrorType(Status status, int minCode, int maxCode) {
            this.status = status;
            this.minCode = minCode;
            this.maxCode = maxCode;
        }

        public boolean contains(int code) {
            return code >= minCode && code <= maxCode;
        }

        public Status getStatus() {
            return status;
        }
    }

    /**
     * 将业务异常转换为gRPC异常
     *
     * @param e 业务异常
     * @return gRPC异常
     */
    public static StatusRuntimeException convertException(BizException e) {
        int errorCode = e.getErrorCodeInt();
        
        // 查找匹配的错误类型
        for (ErrorType errorType : ErrorType.values()) {
            if (errorType.contains(errorCode)) {
                return createStatusException(errorType.getStatus(), e);
            }
        }
        
        // 默认使用 UNKNOWN 状态
        return Status.UNKNOWN.withDescription(e.getMessage()).asRuntimeException();
    }

    /**
     * 创建带有业务错误码的 StatusRuntimeException
     * 
     * @param status gRPC 状态
     * @param e 业务异常
     * @return gRPC 状态运行时异常
     */
    private static StatusRuntimeException createStatusException(Status status, BizException e) {
        Metadata trailers = new Metadata();
        trailers.put(ERROR_CODE_KEY, e.getErrorCode());
        return status.withDescription(e.getMessage()).withCause(e).asRuntimeException(trailers);
    }

    /**
     * 返回 gRPC 成功状态
     * 
     * @return 成功状态
     */
    public static com.google.rpc.Status success() {
        return com.google.rpc.Status.newBuilder()
                .setCode(Code.OK_VALUE)
                .setMessage("success")
                .build();
    }

    /**
     * 处理 gRPC 字符串默认值，将空字符串转换为 null
     * 
     * @param value 字符串值
     * @return 处理后的字符串，空值返回 null
     */
    public static String handleString(String value) {
        return Optional.ofNullable(value)
                .filter(s -> !s.isEmpty())
                .orElse(null);
    }

    /**
     * 处理 gRPC 整数默认值，将非正数转换为 null
     * 
     * @param value 整数值
     * @return 处理后的整数，非正值返回 null
     */
    public static Integer handleInt(int value) {
        return value > 0 ? value : null;
    }

    /**
     * 处理 gRPC 布尔值默认值
     * 
     * @param value 布尔值包装对象
     * @return 处理后的布尔值，默认实例返回 null
     */
    public static Boolean handleBool(BoolValue value) {
        return value == BoolValue.getDefaultInstance() ? null : value.getValue();
    }

}
