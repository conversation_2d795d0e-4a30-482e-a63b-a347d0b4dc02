package com.blsc.marketing.config;

import com.blsc.cache.config.RedissonConfig;
import com.blsc.commons.id.IdWorkerCommon;
import org.redisson.api.RedissonClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.io.IOException;

//将当前类标识为一个配置类
@Configuration
//扫描组件
public class Config {

    @Value("${id.worker}")
    long idWorker;

    @Value("${redis.env}")
    private String redisEnv;

    /**
     * 雪花 id 生成组件
     */
    @Bean
    public IdWorkerCommon idWorkerCommon() {
        return new IdWorkerCommon(idWorker);
    }


    @Bean
    public RedissonClient redissonCommon() throws IOException {
        RedissonConfig redissonConfig = new RedissonConfig();
        RedissonClient redissonClient = redissonConfig.redissonClient(String.format("redisson-%s.yml", redisEnv));
        return redissonClient;
    }

}
