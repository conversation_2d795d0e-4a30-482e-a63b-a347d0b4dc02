syntax = "proto3";

package com.blsc.marketing;

option java_package = "com.blsc.marketing.grpc";
option java_outer_classname = "MarketingServiceProto";

// 营销服务
service MarketingService {
  // 计算营销价格
  rpc CalculatePrice(CalculatePriceRequest) returns (CalculatePriceResponse);
  
  // 推荐优惠券
  rpc RecommendCoupons(RecommendCouponsRequest) returns (RecommendCouponsResponse);
  
  // 批量计算营销价格
  rpc BatchCalculatePrice(BatchCalculatePriceRequest) returns (BatchCalculatePriceResponse);
}

// 优惠券服务
service CouponService {
  // 用户领取优惠券
  rpc ClaimCoupon(ClaimCouponRequest) returns (ClaimCouponResponse);
  
  // 查询用户优惠券列表
  rpc QueryUserCoupons(QueryUserCouponsRequest) returns (QueryUserCouponsResponse);
  
  // 根据优惠券码查询优惠券详情
  rpc GetCouponByCode(GetCouponByCodeRequest) returns (GetCouponByCodeResponse);
  
  // 验证优惠券可用性
  rpc ValidateCoupon(ValidateCouponRequest) returns (ValidateCouponResponse);
  
  // 使用优惠券
  rpc UseCoupon(UseCouponRequest) returns (UseCouponResponse);
  
  // 查询用户可用的优惠券
  rpc GetAvailableCoupons(GetAvailableCouponsRequest) returns (GetAvailableCouponsResponse);
}

// 优惠券模板服务
service CouponTemplateService {
  // 创建优惠券模板
  rpc CreateTemplate(CreateTemplateRequest) returns (CreateTemplateResponse);
  
  // 查询优惠券模板
  rpc QueryTemplates(QueryTemplatesRequest) returns (QueryTemplatesResponse);
  
  // 根据ID查询优惠券模板
  rpc GetTemplateById(GetTemplateByIdRequest) returns (GetTemplateByIdResponse);
  
  // 更新优惠券模板
  rpc UpdateTemplate(UpdateTemplateRequest) returns (UpdateTemplateResponse);
  
  // 启用/禁用优惠券模板
  rpc UpdateTemplateStatus(UpdateTemplateStatusRequest) returns (UpdateTemplateStatusResponse);
}

// ==================== 营销价格计算相关 ====================

// 计算营销价格请求
message CalculatePriceRequest {
  string user_id = 1;
  string app_id = 2;
  int32 original_amount = 3;  // 原始金额(分)
  repeated string coupon_codes = 4;
  string product_id = 5;
  string order_id = 6;
  bool pre_calculate = 7;  // 是否只是预计算
}

// 计算营销价格响应
message CalculatePriceResponse {
  int32 original_amount = 1;  // 原始金额(分)
  int32 total_discount_amount = 2;  // 总优惠金额(分)
  int32 final_amount = 3;  // 最终金额(分)
  repeated CouponDiscountDetail coupon_details = 4;
  int32 activity_discount_amount = 5;  // 活动优惠金额(分)
  bool success = 6;
  string message = 7;
}

// 优惠券优惠详情
message CouponDiscountDetail {
  string coupon_code = 1;
  int64 template_id = 2;
  string template_name = 3;
  int32 template_type = 4;
  int32 discount_amount = 5;
  bool used = 6;
  string fail_reason = 7;
}

// 推荐优惠券请求
message RecommendCouponsRequest {
  string user_id = 1;
  string app_id = 2;
  int32 order_amount = 3;  // 订单金额(分)
  string product_id = 4;
  int32 limit = 5;  // 推荐数量限制
}

// 推荐优惠券响应
message RecommendCouponsResponse {
  repeated RecommendedCoupon recommended_coupons = 1;
  int32 best_combination_discount = 2;
  repeated string best_combination_coupons = 3;
}

// 推荐优惠券详情
message RecommendedCoupon {
  string coupon_code = 1;
  string template_name = 2;
  int32 template_type = 3;
  int32 estimated_discount = 4;
  string condition_desc = 5;
  string expire_time = 6;
  int32 priority = 7;
}

// 批量计算营销价格请求
message BatchCalculatePriceRequest {
  repeated CalculatePriceRequest requests = 1;
}

// 批量计算营销价格响应
message BatchCalculatePriceResponse {
  repeated CalculatePriceResponse responses = 1;
  int32 success_count = 2;
  int32 fail_count = 3;
}

// ==================== 优惠券相关 ====================

// 领取优惠券请求
message ClaimCouponRequest {
  int64 template_id = 1;
  string user_id = 2;
  string app_id = 3;
}

// 领取优惠券响应
message ClaimCouponResponse {
  string coupon_code = 1;
  bool success = 2;
  string message = 3;
}

// 查询用户优惠券请求
message QueryUserCouponsRequest {
  string user_id = 1;
  string app_id = 2;
  int32 status = 3;
  int32 template_type = 4;
  int32 page_num = 5;
  int32 page_size = 6;
}

// 查询用户优惠券响应
message QueryUserCouponsResponse {
  repeated UserCoupon coupons = 1;
  int32 total = 2;
  int32 page_num = 3;
  int32 page_size = 4;
}

// 用户优惠券
message UserCoupon {
  int64 id = 1;
  string coupon_code = 2;
  int64 template_id = 3;
  string template_name = 4;
  int32 template_type = 5;
  int32 scope_type = 6;
  string scope_value = 7;
  int32 discount_amount = 8;
  double discount_rate = 9;
  int32 min_amount = 10;
  int32 max_discount = 11;
  string user_id = 12;
  string app_id = 13;
  int32 status = 14;
  string used_time = 15;
  string used_order_id = 16;
  string expire_time = 17;
  string create_time = 18;
  string description = 19;
}

// 根据优惠券码查询请求
message GetCouponByCodeRequest {
  string coupon_code = 1;
}

// 根据优惠券码查询响应
message GetCouponByCodeResponse {
  UserCoupon coupon = 1;
  bool found = 2;
}

// 验证优惠券请求
message ValidateCouponRequest {
  string coupon_code = 1;
  string user_id = 2;
  string app_id = 3;
  int32 order_amount = 4;
  string product_id = 5;
}

// 验证优惠券响应
message ValidateCouponResponse {
  bool valid = 1;
  string message = 2;
  int32 discount_amount = 3;
  int32 final_amount = 4;
}

// 使用优惠券请求
message UseCouponRequest {
  string coupon_code = 1;
  string user_id = 2;
  string app_id = 3;
  string order_id = 4;
  int32 original_amount = 5;
  string product_id = 6;
}

// 使用优惠券响应
message UseCouponResponse {
  bool success = 1;
  string message = 2;
  int32 discount_amount = 3;
  int32 final_amount = 4;
}

// 查询可用优惠券请求
message GetAvailableCouponsRequest {
  string user_id = 1;
  string app_id = 2;
}

// 查询可用优惠券响应
message GetAvailableCouponsResponse {
  repeated UserCoupon coupons = 1;
}

// ==================== 优惠券模板相关 ====================

// 创建优惠券模板请求
message CreateTemplateRequest {
  string template_name = 1;
  int32 template_type = 2;
  int32 scope_type = 3;
  string scope_value = 4;
  int32 discount_amount = 5;
  double discount_rate = 6;
  int32 min_amount = 7;
  int32 max_discount = 8;
  int32 total_count = 9;
  int32 per_user_limit = 10;
  string valid_start_time = 11;
  string valid_end_time = 12;
  string description = 13;
  string create_by = 14;
}

// 创建优惠券模板响应
message CreateTemplateResponse {
  int64 template_id = 1;
  bool success = 2;
  string message = 3;
}

// 查询优惠券模板请求
message QueryTemplatesRequest {
  string template_name = 1;
  int32 template_type = 2;
  int32 scope_type = 3;
  int32 status = 4;
  int32 page_num = 5;
  int32 page_size = 6;
}

// 查询优惠券模板响应
message QueryTemplatesResponse {
  repeated CouponTemplate templates = 1;
  int32 total = 2;
  int32 page_num = 3;
  int32 page_size = 4;
}

// 优惠券模板
message CouponTemplate {
  int64 id = 1;
  string template_name = 2;
  int32 template_type = 3;
  int32 scope_type = 4;
  string scope_value = 5;
  int32 discount_amount = 6;
  double discount_rate = 7;
  int32 min_amount = 8;
  int32 max_discount = 9;
  int32 total_count = 10;
  int32 issued_count = 11;
  int32 remaining_count = 12;
  int32 per_user_limit = 13;
  string valid_start_time = 14;
  string valid_end_time = 15;
  int32 status = 16;
  string description = 17;
  string create_time = 18;
  string create_by = 19;
}

// 根据ID查询模板请求
message GetTemplateByIdRequest {
  int64 template_id = 1;
}

// 根据ID查询模板响应
message GetTemplateByIdResponse {
  CouponTemplate template = 1;
  bool found = 2;
}

// 更新优惠券模板请求
message UpdateTemplateRequest {
  int64 template_id = 1;
  CreateTemplateRequest template_data = 2;
}

// 更新优惠券模板响应
message UpdateTemplateResponse {
  bool success = 1;
  string message = 2;
}

// 更新模板状态请求
message UpdateTemplateStatusRequest {
  int64 template_id = 1;
  int32 status = 2;
  string update_by = 3;
}

// 更新模板状态响应
message UpdateTemplateStatusResponse {
  bool success = 1;
  string message = 2;
}
