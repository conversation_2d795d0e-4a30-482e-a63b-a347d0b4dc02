package com.blsc.marketing.coupon.service.impl;

import com.blsc.marketing.coupon.bean.bo.CouponTemplateBO;
import com.blsc.marketing.coupon.bean.po.CouponTemplatePO;
import com.blsc.marketing.coupon.mapper.CouponTemplateMapper;
import com.blsc.marketing.coupon.mapper.UserCouponMapper;
import com.blsc.marketing.coupon.service.CouponCacheService;
import com.blsc.marketing.coupon.service.CouponTemplateService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 缓存优化测试
 * 
 * 测试重点：
 * 1. 验证用户领取限制检查时缓存失效的处理逻辑
 * 2. 验证只缓存计数信息的策略
 * 3. 验证缓存一致性
 */
@ExtendWith(MockitoExtension.class)
class CouponCacheOptimizationTest {

    @Mock
    private CouponCacheService couponCacheService;

    @Mock
    private UserCouponMapper userCouponMapper;

    @Mock
    private CouponTemplateMapper couponTemplateMapper;

    @InjectMocks
    private CouponTemplateServiceImpl couponTemplateService;

    private CouponTemplateBO mockTemplate;

    @BeforeEach
    void setUp() {
        mockTemplate = new CouponTemplateBO();
        mockTemplate.setId(1L);
        mockTemplate.setTemplateName("测试优惠券");
        mockTemplate.setStatus(1);
        mockTemplate.setValidStartTime(LocalDateTime.now().minusDays(1));
        mockTemplate.setValidEndTime(LocalDateTime.now().plusDays(30));
        mockTemplate.setTotalCount(1000);
        mockTemplate.setPerUserLimit(3);
    }

    @Test
    void testCanIssueToUser_CacheHit() {
        // 模拟数据库查询模板
        CouponTemplatePO templatePO = new CouponTemplatePO();
        templatePO.setId(1L);
        templatePO.setStatus(1);
        templatePO.setValidStartTime(LocalDateTime.now().minusDays(1));
        templatePO.setValidEndTime(LocalDateTime.now().plusDays(30));
        templatePO.setTotalCount(1000);
        templatePO.setPerUserLimit(3);

        when(couponTemplateMapper.selectById(1L)).thenReturn(templatePO);
        when(couponCacheService.getIssuedCount(1L)).thenReturn(100L);
        when(couponCacheService.getUserClaimedCount("user123", 1L)).thenReturn(2L);

        // 执行测试
        Boolean canIssue = couponTemplateService.canIssueToUser(1L, "user123");

        // 验证结果
        assertTrue(canIssue);

        // 验证缓存被调用，数据库没有被调用
        verify(couponCacheService).getUserClaimedCount("user123", 1L);
        verify(userCouponMapper, never()).countUserClaimedCoupons(anyString(), anyLong());
    }

    @Test
    void testCanIssueToUser_CacheMiss() {
        // 模拟数据库查询模板
        CouponTemplatePO templatePO = new CouponTemplatePO();
        templatePO.setId(1L);
        templatePO.setStatus(1);
        templatePO.setValidStartTime(LocalDateTime.now().minusDays(1));
        templatePO.setValidEndTime(LocalDateTime.now().plusDays(30));
        templatePO.setTotalCount(1000);
        templatePO.setPerUserLimit(3);

        when(couponTemplateMapper.selectById(1L)).thenReturn(templatePO);
        when(couponCacheService.getIssuedCount(1L)).thenReturn(100L);
        when(couponCacheService.getUserClaimedCount("user123", 1L)).thenReturn(null); // 缓存未命中
        when(userCouponMapper.countUserClaimedCoupons("user123", 1L)).thenReturn(1);

        // 执行测试
        Boolean canIssue = couponTemplateService.canIssueToUser(1L, "user123");

        // 验证结果
        assertTrue(canIssue);

        // 验证缓存未命中时从数据库查询
        verify(couponCacheService).getUserClaimedCount("user123", 1L);
        verify(userCouponMapper).countUserClaimedCoupons("user123", 1L);
        verify(couponCacheService).setUserClaimedCount("user123", 1L, 1L);
    }

    @Test
    void testCanIssueToUser_ExceedUserLimit_CacheMiss() {
        // 模拟数据库查询模板
        CouponTemplatePO templatePO = new CouponTemplatePO();
        templatePO.setId(1L);
        templatePO.setStatus(1);
        templatePO.setValidStartTime(LocalDateTime.now().minusDays(1));
        templatePO.setValidEndTime(LocalDateTime.now().plusDays(30));
        templatePO.setTotalCount(1000);
        templatePO.setPerUserLimit(3);

        when(couponTemplateMapper.selectById(1L)).thenReturn(templatePO);
        when(couponCacheService.getIssuedCount(1L)).thenReturn(100L);
        when(couponCacheService.getUserClaimedCount("user123", 1L)).thenReturn(null); // 缓存未命中
        when(userCouponMapper.countUserClaimedCoupons("user123", 1L)).thenReturn(3); // 已达到限制

        // 执行测试
        Boolean canIssue = couponTemplateService.canIssueToUser(1L, "user123");

        // 验证结果
        assertFalse(canIssue);

        // 验证从数据库查询并缓存结果
        verify(userCouponMapper).countUserClaimedCoupons("user123", 1L);
        verify(couponCacheService).setUserClaimedCount("user123", 1L, 3L);
    }

    @Test
    void testCanIssueToUser_ExceedUserLimit_CacheHit() {
        // 模拟数据库查询模板
        CouponTemplatePO templatePO = new CouponTemplatePO();
        templatePO.setId(1L);
        templatePO.setStatus(1);
        templatePO.setValidStartTime(LocalDateTime.now().minusDays(1));
        templatePO.setValidEndTime(LocalDateTime.now().plusDays(30));
        templatePO.setTotalCount(1000);
        templatePO.setPerUserLimit(3);

        when(couponTemplateMapper.selectById(1L)).thenReturn(templatePO);
        when(couponCacheService.getIssuedCount(1L)).thenReturn(100L);
        when(couponCacheService.getUserClaimedCount("user123", 1L)).thenReturn(3L); // 缓存命中，已达到限制

        // 执行测试
        Boolean canIssue = couponTemplateService.canIssueToUser(1L, "user123");

        // 验证结果
        assertFalse(canIssue);

        // 验证只调用缓存，不调用数据库
        verify(couponCacheService).getUserClaimedCount("user123", 1L);
        verify(userCouponMapper, never()).countUserClaimedCoupons(anyString(), anyLong());
        verify(couponCacheService, never()).setUserClaimedCount(anyString(), anyLong(), anyLong());
    }

    @Test
    void testCanIssueToUser_StockInsufficient() {
        // 模拟数据库查询模板
        CouponTemplatePO templatePO = new CouponTemplatePO();
        templatePO.setId(1L);
        templatePO.setStatus(1);
        templatePO.setValidStartTime(LocalDateTime.now().minusDays(1));
        templatePO.setValidEndTime(LocalDateTime.now().plusDays(30));
        templatePO.setTotalCount(1000);
        templatePO.setPerUserLimit(3);

        when(couponTemplateMapper.selectById(1L)).thenReturn(templatePO);
        when(couponCacheService.getIssuedCount(1L)).thenReturn(1000L); // 已发放完毕

        // 执行测试
        Boolean canIssue = couponTemplateService.canIssueToUser(1L, "user123");

        // 验证结果
        assertFalse(canIssue);

        // 验证不会检查用户领取限制
        verify(couponCacheService, never()).getUserClaimedCount(anyString(), anyLong());
        verify(userCouponMapper, never()).countUserClaimedCoupons(anyString(), anyLong());
    }

    @Test
    void testCanIssueToUser_NullParameters() {
        // 测试空参数
        assertFalse(couponTemplateService.canIssueToUser(null, "user123"));
        assertFalse(couponTemplateService.canIssueToUser(1L, null));
        assertFalse(couponTemplateService.canIssueToUser(1L, ""));
        
        // 验证没有调用任何缓存或数据库方法
        verifyNoInteractions(couponCacheService);
        verifyNoInteractions(userCouponMapper);
    }


}
