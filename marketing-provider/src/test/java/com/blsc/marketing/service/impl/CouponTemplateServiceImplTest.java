package com.blsc.marketing.service.impl;

import com.blsc.marketing.coupon.bean.bo.CouponTemplateBO;
import com.blsc.marketing.coupon.bean.dto.CreateCouponTemplateDTO;
import com.blsc.marketing.coupon.bean.po.CouponTemplatePO;
import com.blsc.marketing.coupon.mapper.CouponTemplateMapper;
import com.blsc.marketing.coupon.mapper.UserCouponMapper;
import com.blsc.marketing.coupon.service.CouponCacheService;
import com.blsc.marketing.coupon.service.impl.CouponTemplateServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * 优惠券模板服务测试
 */
@ExtendWith(MockitoExtension.class)
class CouponTemplateServiceImplTest {
    
    @Mock
    private CouponTemplateMapper couponTemplateMapper;

    @Mock
    private UserCouponMapper userCouponMapper;

    @Mock
    private CouponCacheService couponCacheService;
    
    @InjectMocks
    private CouponTemplateServiceImpl couponTemplateService;
    
    private CreateCouponTemplateDTO createTemplateDTO;
    private CouponTemplatePO templatePO;
    
    @BeforeEach
    void setUp() {
        // 准备测试数据
        createTemplateDTO = new CreateCouponTemplateDTO();
        createTemplateDTO.setTemplateName("测试满减券");
        createTemplateDTO.setTemplateType(1); // 满减券
        createTemplateDTO.setScopeType(1); // 平台级
        createTemplateDTO.setDiscountAmount(1000); // 10元
        createTemplateDTO.setMinAmount(5000); // 满50元
        createTemplateDTO.setTotalCount(1000);
        createTemplateDTO.setPerUserLimit(1);
        createTemplateDTO.setValidStartTime(LocalDateTime.now());
        createTemplateDTO.setValidEndTime(LocalDateTime.now().plusDays(30));
        createTemplateDTO.setDescription("测试满减券");
        createTemplateDTO.setCreateBy("test_user");
        
        templatePO = new CouponTemplatePO();
        templatePO.setId(1L);
        templatePO.setTemplateName("测试满减券");
        templatePO.setTemplateType(1);
        templatePO.setScopeType(1);
        templatePO.setDiscountAmount(1000);
        templatePO.setMinAmount(5000);
        templatePO.setTotalCount(1000);
        // issuedCount和remainingCount字段不在数据库表中
        templatePO.setPerUserLimit(1);
        templatePO.setValidStartTime(LocalDateTime.now());
        templatePO.setValidEndTime(LocalDateTime.now().plusDays(30));
        templatePO.setStatus(1);
        templatePO.setDescription("测试满减券");
        templatePO.setCreateTime(LocalDateTime.now());
        templatePO.setUpdateTime(LocalDateTime.now());
        // createBy和updateBy字段不在数据库表中
    }
    
    @Test
    void testCreateTemplate_Success() {
        // 模拟数据库插入成功
        when(couponTemplateMapper.insertOne(any(CouponTemplatePO.class))).thenAnswer(invocation -> {
            CouponTemplatePO template = invocation.getArgument(0);
            template.setId(1L);
            return 1;
        });
        
        // 执行测试
        Long templateId = couponTemplateService.createTemplate(createTemplateDTO);
        
        // 验证结果
        assertNotNull(templateId);
        assertEquals(1L, templateId);
        
        // 验证方法调用
        verify(couponTemplateMapper, times(1)).insertOne(any(CouponTemplatePO.class));
    }
    
    @Test
    void testGetTemplateById_DirectQuery() {
        // 模拟数据库查询（已移除模板缓存）
        CouponTemplatePO templatePO = new CouponTemplatePO();
        templatePO.setId(1L);
        templatePO.setTemplateName("测试满减券");
        templatePO.setTemplateType(1);
        templatePO.setStatus(1);

        when(couponTemplateMapper.selectById(1L)).thenReturn(templatePO);

        // 执行测试
        CouponTemplateBO result = couponTemplateService.getTemplateById(1L);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getId());
        assertEquals("测试满减券", result.getTemplateName());

        // 验证直接查询数据库
        verify(couponTemplateMapper).selectById(1L);
    }
    
    @Test
    void testGetTemplateById_NotFound() {
        // 模拟数据库中没有数据
        when(couponTemplateMapper.selectById(1L)).thenReturn(null);

        // 执行测试并验证异常
        assertThrows(RuntimeException.class, () -> {
            couponTemplateService.getTemplateById(1L);
        });

        // 验证方法调用
        verify(couponTemplateMapper, times(1)).selectById(1L);
    }
    
    @Test
    void testCanIssueToUser_ValidTemplate() {
        // 模拟数据库查询模板
        CouponTemplatePO templatePO = new CouponTemplatePO();
        templatePO.setId(1L);
        templatePO.setStatus(1);
        templatePO.setValidStartTime(LocalDateTime.now().minusDays(1));
        templatePO.setValidEndTime(LocalDateTime.now().plusDays(30));
        templatePO.setTotalCount(1000);
        templatePO.setPerUserLimit(1);

        when(couponTemplateMapper.selectById(1L)).thenReturn(templatePO);
        when(couponCacheService.getIssuedCount(1L)).thenReturn(100L); // 已发放100张
        when(couponCacheService.getUserClaimedCount("user123", 1L)).thenReturn(0L);

        // 执行测试
        Boolean canIssue = couponTemplateService.canIssueToUser(1L, "user123");

        // 验证结果
        assertTrue(canIssue);
    }
    
    @Test
    void testCanIssueToUser_ExceedUserLimit() {
        // 模拟数据库查询模板
        CouponTemplatePO templatePO = new CouponTemplatePO();
        templatePO.setId(1L);
        templatePO.setStatus(1);
        templatePO.setValidStartTime(LocalDateTime.now().minusDays(1));
        templatePO.setValidEndTime(LocalDateTime.now().plusDays(30));
        templatePO.setTotalCount(1000);
        templatePO.setPerUserLimit(1);

        when(couponTemplateMapper.selectById(1L)).thenReturn(templatePO);
        when(couponCacheService.getIssuedCount(1L)).thenReturn(100L);
        when(couponCacheService.getUserClaimedCount("user123", 1L)).thenReturn(1L);

        // 执行测试
        Boolean canIssue = couponTemplateService.canIssueToUser(1L, "user123");

        // 验证结果
        assertFalse(canIssue);
    }
    
    @Test
    void testEnableTemplate_Success() {
        // 模拟模板存在
        when(couponTemplateMapper.selectById(1L)).thenReturn(templatePO);
        when(couponTemplateMapper.updateById(any(CouponTemplatePO.class))).thenReturn(1);
        
        // 执行测试
        Boolean result = couponTemplateService.enableTemplate(1L, "admin");
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(couponTemplateMapper, times(1)).selectById(1L);
        verify(couponTemplateMapper, times(1)).updateById(any(CouponTemplatePO.class));
        verify(couponCacheService, times(1)).evictIssuedCount(1L);
    }
    
    @Test
    void testDeleteTemplate_Success() {
        // 模拟模板存在且未发放
        when(userCouponMapper.countIssuedCoupons(1L)).thenReturn(0);
        when(couponTemplateMapper.selectById(1L)).thenReturn(templatePO);
        when(couponTemplateMapper.deleteById(1L)).thenReturn(1);
        
        // 执行测试
        Boolean result = couponTemplateService.deleteTemplate(1L);
        
        // 验证结果
        assertTrue(result);
        
        // 验证方法调用
        verify(couponTemplateMapper, times(1)).selectById(1L);
        verify(couponTemplateMapper, times(1)).deleteById(1L);
        verify(couponCacheService, times(1)).evictIssuedCount(1L);
    }
}
