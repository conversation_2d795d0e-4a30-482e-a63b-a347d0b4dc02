package com.blsc.marketing.service.impl;

import com.blsc.marketing.coupon.bean.bo.CalculatePriceBO;
import com.blsc.marketing.coupon.bean.bo.UserCouponBO;
import com.blsc.marketing.coupon.bean.dto.CalculatePriceDTO;
import com.blsc.marketing.coupon.service.CouponService;
import com.blsc.marketing.coupon.service.impl.MarketingPriceServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * 营销价格服务测试类
 */
@ExtendWith(MockitoExtension.class)
class MarketingPriceServiceImplTest {
    
    @Mock
    private CouponService couponService;
    
    @InjectMocks
    private MarketingPriceServiceImpl marketingPriceService;
    
    private UserCouponBO fullReductionCoupon;
    private UserCouponBO discountCoupon;
    private UserCouponBO directReductionCoupon;
    
    @BeforeEach
    void setUp() {
        // 创建满减券测试数据
        fullReductionCoupon = new UserCouponBO();
        fullReductionCoupon.setCouponCode("TEST001");
        fullReductionCoupon.setTemplateId(1L);
        fullReductionCoupon.setTemplateName("满100减20券");
        fullReductionCoupon.setTemplateType(1); // 满减券
        fullReductionCoupon.setScopeType(1); // 平台级
        fullReductionCoupon.setDiscountAmount(2000); // 20元
        fullReductionCoupon.setMinAmount(10000); // 满100元
        fullReductionCoupon.setUserId("user123");
        fullReductionCoupon.setAppId("app001");
        fullReductionCoupon.setStatus(1); // 未使用
        // validStartTime和validEndTime字段已删除，使用expireTime
        fullReductionCoupon.setExpireTime(LocalDateTime.now().plusDays(30));
        
        // 创建折扣券测试数据
        discountCoupon = new UserCouponBO();
        discountCoupon.setCouponCode("TEST002");
        discountCoupon.setTemplateId(2L);
        discountCoupon.setTemplateName("9折券");
        discountCoupon.setTemplateType(2); // 折扣券
        discountCoupon.setScopeType(1); // 平台级
        discountCoupon.setDiscountRate(0.9); // 9折
        discountCoupon.setMaxDiscount(5000); // 最大优惠50元
        discountCoupon.setUserId("user123");
        discountCoupon.setAppId("app001");
        discountCoupon.setStatus(1); // 未使用
        // validStartTime和validEndTime字段已删除，使用expireTime
        discountCoupon.setExpireTime(LocalDateTime.now().plusDays(30));
        
        // 创建立减券测试数据
        directReductionCoupon = new UserCouponBO();
        directReductionCoupon.setCouponCode("TEST003");
        directReductionCoupon.setTemplateId(3L);
        directReductionCoupon.setTemplateName("立减10元券");
        directReductionCoupon.setTemplateType(3); // 立减券
        directReductionCoupon.setScopeType(1); // 平台级
        directReductionCoupon.setDiscountAmount(1000); // 立减10元
        directReductionCoupon.setUserId("user123");
        directReductionCoupon.setAppId("app001");
        directReductionCoupon.setStatus(1); // 未使用
        // validStartTime和validEndTime字段已删除，使用expireTime
        directReductionCoupon.setExpireTime(LocalDateTime.now().plusDays(30));
    }
    
    @Test
    void testCalculatePriceWithoutCoupons() {
        // 测试无优惠券的价格计算
        CalculatePriceDTO dto = new CalculatePriceDTO();
        dto.setUserId("user123");
        dto.setAppId("app001");
        dto.setOriginalAmount(15000); // 150元
        dto.setPreCalculate(true);
        
        CalculatePriceBO result = marketingPriceService.calculatePrice(dto);
        
        assertTrue(result.getSuccess());
        assertEquals(15000, result.getOriginalAmount());
        assertEquals(0, result.getTotalDiscountAmount());
        assertEquals(15000, result.getFinalAmount());
    }
    
    @Test
    void testCalculatePriceWithFullReductionCoupon() {
        // 测试满减券价格计算
        CalculatePriceDTO dto = new CalculatePriceDTO();
        dto.setUserId("user123");
        dto.setAppId("app001");
        dto.setOriginalAmount(15000); // 150元，满足满100减20的条件
        dto.setCouponCodes(Arrays.asList("TEST001"));
        dto.setPreCalculate(true);
        
        when(couponService.getCouponsByCodes(anyList())).thenReturn(Arrays.asList(fullReductionCoupon));
        
        CalculatePriceBO result = marketingPriceService.calculatePrice(dto);
        
        assertTrue(result.getSuccess());
        assertEquals(15000, result.getOriginalAmount());
        assertEquals(2000, result.getTotalDiscountAmount()); // 减20元
        assertEquals(13000, result.getFinalAmount()); // 130元
        assertNotNull(result.getCouponDetails());
        assertEquals(1, result.getCouponDetails().size());
    }
    
    @Test
    void testCalculatePriceWithDiscountCoupon() {
        // 测试折扣券价格计算
        CalculatePriceDTO dto = new CalculatePriceDTO();
        dto.setUserId("user123");
        dto.setAppId("app001");
        dto.setOriginalAmount(10000); // 100元
        dto.setCouponCodes(Arrays.asList("TEST002"));
        dto.setPreCalculate(true);
        
        when(couponService.getCouponsByCodes(anyList())).thenReturn(Arrays.asList(discountCoupon));
        
        CalculatePriceBO result = marketingPriceService.calculatePrice(dto);
        
        assertTrue(result.getSuccess());
        assertEquals(10000, result.getOriginalAmount());
        assertEquals(999, result.getTotalDiscountAmount()); // 9折，优惠约10元（精度问题）
        assertEquals(9001, result.getFinalAmount()); // 约90元
    }
    
    @Test
    void testCalculatePriceWithDirectReductionCoupon() {
        // 测试立减券价格计算
        CalculatePriceDTO dto = new CalculatePriceDTO();
        dto.setUserId("user123");
        dto.setAppId("app001");
        dto.setOriginalAmount(5000); // 50元
        dto.setCouponCodes(Arrays.asList("TEST003"));
        dto.setPreCalculate(true);
        
        when(couponService.getCouponsByCodes(anyList())).thenReturn(Arrays.asList(directReductionCoupon));
        
        CalculatePriceBO result = marketingPriceService.calculatePrice(dto);
        
        assertTrue(result.getSuccess());
        assertEquals(5000, result.getOriginalAmount());
        assertEquals(1000, result.getTotalDiscountAmount()); // 立减10元
        assertEquals(4000, result.getFinalAmount()); // 40元
    }
    
    @Test
    void testCalculatePriceWithInvalidCoupon() {
        // 测试不满足使用条件的优惠券
        CalculatePriceDTO dto = new CalculatePriceDTO();
        dto.setUserId("user123");
        dto.setAppId("app001");
        dto.setOriginalAmount(5000); // 50元，不满足满100减20的条件
        dto.setCouponCodes(Arrays.asList("TEST001"));
        dto.setPreCalculate(true);
        
        when(couponService.getCouponsByCodes(anyList())).thenReturn(Arrays.asList(fullReductionCoupon));
        
        CalculatePriceBO result = marketingPriceService.calculatePrice(dto);
        
        assertTrue(result.getSuccess());
        assertEquals(5000, result.getOriginalAmount());
        assertEquals(0, result.getTotalDiscountAmount()); // 不满足条件，无优惠
        assertEquals(5000, result.getFinalAmount());
    }
    
    @Test
    void testGetAvailableCoupons() {
        // 测试获取可用优惠券
        when(couponService.getAvailableCoupons(anyString(), anyString()))
                .thenReturn(Arrays.asList(fullReductionCoupon, discountCoupon, directReductionCoupon));
        
        List<UserCouponBO> availableCoupons = couponService.getAvailableCoupons("user123", "app001");
        
        assertNotNull(availableCoupons);
        assertEquals(3, availableCoupons.size());
    }
    
    @Test
    void testPreCalculatePrice() {
        // 测试预计算价格
        CalculatePriceDTO dto = new CalculatePriceDTO();
        dto.setUserId("user123");
        dto.setAppId("app001");
        dto.setOriginalAmount(15000);
        dto.setCouponCodes(Arrays.asList("TEST001"));
        
        when(couponService.getCouponsByCodes(anyList())).thenReturn(Arrays.asList(fullReductionCoupon));
        
        CalculatePriceBO result = marketingPriceService.preCalculatePrice(dto);
        
        assertTrue(result.getSuccess());
        assertTrue(result.getPreCalculate());
        assertEquals(15000, result.getOriginalAmount());
        assertEquals(2000, result.getTotalDiscountAmount());
        assertEquals(13000, result.getFinalAmount());
    }
}
