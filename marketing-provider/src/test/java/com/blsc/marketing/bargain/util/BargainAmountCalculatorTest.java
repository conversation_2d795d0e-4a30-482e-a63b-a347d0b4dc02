package com.blsc.marketing.bargain.util;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;

import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 砍价金额计算器测试类
 */
@DisplayName("砍价金额计算器测试")
class BargainAmountCalculatorTest {

    @Test
    @DisplayName("基础砍价计算测试 - 365元商品砍到299元，10人阈值")
    void testBasicBargainCalculation() {
        // 商品原价365元，底价299元，砍价总额66元，10人阈值
        int maxBargainAmount = 6600; // 66元 = 6600分
        int minAssistCount = 10;
        long baseSeed = 12340L;
        
        BargainAmountCalculator.BargainValidationResult result = 
                BargainAmountCalculator.validateBargainAlgorithm(maxBargainAmount, minAssistCount, baseSeed);
        
        // 验证算法有效性
        assertTrue(result.isValid(), "砍价算法应该有效");
        assertTrue(result.getTotalBargainedAtThreshold() <= maxBargainAmount, 
                "10次砍价总额不应超过最大砍价金额");
        assertTrue(result.getUtilizationRate() > 0.7, 
                "砍价金额利用率应该大于70%");
        
        System.out.println("砍价验证结果: " + result);
        System.out.println("砍价分布: " + result.getDistribution());
    }

    @Test
    @DisplayName("随机性测试 - 验证不同随机种子产生不同砍价金额")
    void testRandomnessWithDifferentSeeds() {
        int maxBargainAmount = 10000; // 100元
        int minAssistCount = 5;
        int alreadyBargainAmount = 0;
        int assistOrder = 1; // 第1次助力
        
        // 使用不同的随机种子
        long[] seeds = {1000L, 2000L, 3000L, 4000L, 5000L};
        int[] bargainAmounts = new int[seeds.length];
        
        System.out.println("不同随机种子第1次助力的砍价金额对比：");
        for (int i = 0; i < seeds.length; i++) {
            bargainAmounts[i] = BargainAmountCalculator.calculateBargainAmount(
                    assistOrder, maxBargainAmount, minAssistCount, alreadyBargainAmount, seeds[i]);
            System.out.printf("种子%d: %d分(%.2f元)%n", seeds[i], bargainAmounts[i], bargainAmounts[i] / 100.0);
        }
        
        // 验证随机性：不同种子的砍价金额应该不完全相同
        boolean hasVariation = false;
        for (int i = 1; i < bargainAmounts.length; i++) {
            if (bargainAmounts[i] != bargainAmounts[0]) {
                hasVariation = true;
                break;
            }
        }
        assertTrue(hasVariation, "不同随机种子的砍价金额应该有差异");
        
        // 验证合理性：所有砍价金额都应该在合理范围内
        for (int amount : bargainAmounts) {
            assertTrue(amount > 0, "砍价金额应该大于0");
            assertTrue(amount <= maxBargainAmount, "砍价金额不应超过最大值");
        }
        
        System.out.println("✅ 随机性验证通过：不同随机种子产生不同砍价金额");
    }

    @Test
    @DisplayName("一致性测试 - 验证相同随机种子多次计算结果一致")
    void testConsistencyWithSameSeed() {
        int maxBargainAmount = 5000; // 50元
        int minAssistCount = 8;
        int alreadyBargainAmount = 1000; // 已砍10元
        int assistOrder = 3; // 第3次助力
        long randomSeed = 9999L;
        
        // 相同参数多次计算应该得到相同结果
        int firstResult = BargainAmountCalculator.calculateBargainAmount(
                assistOrder, maxBargainAmount, minAssistCount, alreadyBargainAmount, randomSeed);
        
        for (int i = 0; i < 10; i++) {
            int result = BargainAmountCalculator.calculateBargainAmount(
                    assistOrder, maxBargainAmount, minAssistCount, alreadyBargainAmount, randomSeed);
            assertEquals(firstResult, result, 
                    String.format("第%d次计算结果%d应该与首次结果%d一致", i + 1, result, firstResult));
        }
        
        System.out.printf("✅ 一致性验证通过：相同随机种子%d第%d次助力砍价金额始终为%d分%n", 
                randomSeed, assistOrder, firstResult);
    }

    @Test
    @DisplayName("边界条件测试")
    void testBoundaryConditions() {
        long seed = 1111L;
        
        // 测试最小值
        int bargainAmount1 = BargainAmountCalculator.calculateBargainAmount(1, 100, 5, 0, seed);
        assertTrue(bargainAmount1 >= 1, "砍价金额应该至少为1分");
        
        // 测试已砍价金额等于最大值的情况
        int bargainAmount2 = BargainAmountCalculator.calculateBargainAmount(1, 100, 5, 100, seed);
        assertEquals(1, bargainAmount2, "已砍价金额达到最大值时，应该返回最小砍价金额");
        
        // 测试已砍价金额超过最大值的情况
        int bargainAmount3 = BargainAmountCalculator.calculateBargainAmount(1, 100, 5, 150, seed);
        assertEquals(1, bargainAmount3, "已砍价金额超过最大值时，应该返回最小砍价金额");
    }

    @Test
    @DisplayName("实际业务场景测试 - 365元商品砍到299元，验证阈值后快速完成")
    void testRealBusinessScenario() {
        // 实际业务场景：365元商品，底价299元，需要砍价66元，10人阈值
        int originalPrice = 36500; // 365元
        int floorPrice = 29900;    // 299元
        int maxBargainAmount = originalPrice - floorPrice; // 66元 = 6600分
        int minAssistCount = 10;
        long baseSeed = 88888L;
        
        // 模拟完整的砍价过程，包括阈值后的助力
        int totalBargained = 0;
        int completedAtAssist = 0;
        
        for (int i = 1; i <= minAssistCount + 5; i++) { // 最多砍15次
            long randomSeed = baseSeed + i * 1000L; // 为每次助力生成不同的随机种子
            int bargainAmount = BargainAmountCalculator.calculateBargainAmount(
                    i, maxBargainAmount, minAssistCount, totalBargained, randomSeed);
            totalBargained += bargainAmount;
            
            String phase = i <= minAssistCount ? "阈值内" : "阈值后";
            System.out.printf("第%d次助力(%s): 砍价%d分(%.2f元), 累计砍价%d分(%.2f元)%n", 
                    i, phase, bargainAmount, bargainAmount / 100.0, totalBargained, totalBargained / 100.0);
            
            // 如果已经达到最大砍价金额，记录完成时的助力次数并停止
            if (totalBargained >= maxBargainAmount) {
                completedAtAssist = i;
                System.out.printf("🎉 第%d次助力后达到最大砍价金额，活动成功！解锁底价购买！%n", i);
                break;
            }
        }
        
        // 验证阈值后能够快速完成（放宽到5次内）
        int assistAfterThreshold = completedAtAssist - minAssistCount;
        assertTrue(assistAfterThreshold <= 5, 
                String.format("阈值后应该在5次内完成，实际用了%d次", assistAfterThreshold));
        assertTrue(assistAfterThreshold >= 1, 
                String.format("应该至少需要阈值后1次助力，实际用了%d次", assistAfterThreshold));
        
        // 验证精确达到最大砍价金额
        assertEquals(maxBargainAmount, totalBargained, 
                String.format("总砍价金额%d分应该精确等于最大值%d分", totalBargained, maxBargainAmount));
        
        // 计算最终价格
        int finalPrice = originalPrice - totalBargained;
        System.out.printf("✅ 最终价格: %d分(%.2f元), 原价: %d分(%.2f元), 砍价: %d分(%.2f元)%n",
                finalPrice, finalPrice / 100.0, originalPrice, originalPrice / 100.0, 
                totalBargained, totalBargained / 100.0);
        
        // 验证最终价格精确等于底价
        assertEquals(floorPrice, finalPrice, 
                String.format("最终价格%d分应该精确等于底价%d分", finalPrice, floorPrice));
        
        System.out.printf("🎯 验证成功：阈值后%d次助力完成砍价，精确达到底价！%n", assistAfterThreshold);
    }

    @Test
    @DisplayName("真实随机性测试 - 验证每次助力都有不同的砍价金额")
    void testTrueRandomness() {
        int maxBargainAmount = 8000; // 80元
        int minAssistCount = 6;
        int alreadyBargainAmount = 0;
        int assistOrder = 1; // 第1次助力
        
        // 模拟10次不同的助力，每次使用不同的随机种子（模拟真实业务场景）
        System.out.println("模拟10次不同助力的砍价金额（每次使用不同随机种子）：");
        int[] bargainAmounts = new int[10];
        
        for (int i = 0; i < 10; i++) {
            // 模拟真实业务中基于时间戳的随机种子
            long randomSeed = System.currentTimeMillis() + i * 1000L;
            bargainAmounts[i] = BargainAmountCalculator.calculateBargainAmount(
                    assistOrder, maxBargainAmount, minAssistCount, alreadyBargainAmount, randomSeed);
            System.out.printf("第%d次助力: %d分(%.2f元), 种子:%d%n", 
                    i + 1, bargainAmounts[i], bargainAmounts[i] / 100.0, randomSeed);
        }
        
        // 验证随机性：不同助力的砍价金额应该有差异
        boolean hasVariation = false;
        for (int i = 1; i < bargainAmounts.length; i++) {
            if (bargainAmounts[i] != bargainAmounts[0]) {
                hasVariation = true;
                break;
            }
        }
        assertTrue(hasVariation, "不同助力的砍价金额应该有差异");
        
        // 验证合理性：所有砍价金额都应该在合理范围内
        for (int amount : bargainAmounts) {
            assertTrue(amount > 0, "砍价金额应该大于0");
            assertTrue(amount <= maxBargainAmount, "砍价金额不应超过最大值");
        }
        
        System.out.println("✅ 真实随机性验证通过：每次助力都有不同的砍价金额");
    }
}
